using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Interface for spatial indexing and optimization operations.
    /// Critical for performance when dealing with large numbers of elements.
    /// </summary>
    public interface ISpatialHelper
    {
        /// <summary>
        /// Creates a spatial index for efficient proximity searches
        /// </summary>
        /// <param name="fireStoppingElements">Fire stopping elements to index</param>
        /// <param name="structuralElements">Structural elements to index</param>
        /// <param name="serviceElements">Service elements to index</param>
        /// <returns>Spatial index object</returns>
        SpatialIndex CreateSpatialIndex(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            IEnumerable<StructuralElement> structuralElements,
            IEnumerable<ServiceElement> serviceElements);

        /// <summary>
        /// Finds elements within a specified distance of a target element using spatial index
        /// </summary>
        /// <param name="spatialIndex">Spatial index</param>
        /// <param name="targetElement">Target fire stopping element</param>
        /// <param name="searchDistance">Search distance in feet</param>
        /// <returns>Nearby elements</returns>
        SpatialSearchResult FindNearbyElements(
            SpatialIndex spatialIndex,
            FireStoppingElement targetElement,
            double searchDistance);

        /// <summary>
        /// Finds structural elements that could intersect with a fire stopping element
        /// </summary>
        /// <param name="spatialIndex">Spatial index</param>
        /// <param name="fireStoppingElement">Fire stopping element</param>
        /// <param name="tolerance">Intersection tolerance in feet</param>
        /// <returns>Potentially intersecting structural elements</returns>
        IEnumerable<StructuralElement> FindPotentialStructuralIntersections(
            SpatialIndex spatialIndex,
            FireStoppingElement fireStoppingElement,
            double tolerance = 0.1);

        /// <summary>
        /// Finds service elements that could be connected to a fire stopping element
        /// </summary>
        /// <param name="spatialIndex">Spatial index</param>
        /// <param name="fireStoppingElement">Fire stopping element</param>
        /// <param name="connectionTolerance">Connection tolerance in feet</param>
        /// <returns>Potentially connected service elements</returns>
        IEnumerable<ServiceElement> FindPotentialServiceConnections(
            SpatialIndex spatialIndex,
            FireStoppingElement fireStoppingElement,
            double connectionTolerance = 0.2);

        /// <summary>
        /// Finds fire stopping elements that could be clashing or adjacent
        /// </summary>
        /// <param name="spatialIndex">Spatial index</param>
        /// <param name="fireStoppingElement">Fire stopping element</param>
        /// <param name="adjacencyDistance">Adjacency distance in feet</param>
        /// <returns>Potentially clashing or adjacent fire stopping elements</returns>
        IEnumerable<FireStoppingElement> FindPotentialFireStoppingProximity(
            SpatialIndex spatialIndex,
            FireStoppingElement fireStoppingElement,
            double adjacencyDistance);

        /// <summary>
        /// Optimized adjacency check using spatial indexing and caching
        /// </summary>
        /// <param name="spatialIndex">Spatial index</param>
        /// <param name="fireStoppingElement">Fire stopping element to check</param>
        /// <param name="adjacencyThreshold">Distance threshold in millimeters</param>
        /// <returns>Check result, adjacent elements, and nearest distance</returns>
        (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements, double NearestDistance) CheckAdjacentOptimized(
            SpatialIndex spatialIndex,
            FireStoppingElement fireStoppingElement,
            double adjacencyThreshold = 300.0);

        /// <summary>
        /// Optimizes the search order for elements based on spatial proximity
        /// </summary>
        /// <param name="targetElement">Target element</param>
        /// <param name="candidateElements">Candidate elements to sort</param>
        /// <returns>Elements sorted by proximity to target</returns>
        IEnumerable<T> OptimizeSearchOrder<T>(FireStoppingElement targetElement, IEnumerable<T> candidateElements)
            where T : class;

        /// <summary>
        /// Creates a spatial grid for partitioning elements
        /// </summary>
        /// <param name="allElements">All elements to partition</param>
        /// <param name="gridSize">Grid cell size in feet</param>
        /// <returns>Spatial grid</returns>
        SpatialGrid CreateSpatialGrid(IEnumerable<object> allElements, double gridSize = 10.0);

        /// <summary>
        /// Gets elements in the same grid cell as the target element
        /// </summary>
        /// <param name="spatialGrid">Spatial grid</param>
        /// <param name="targetElement">Target element</param>
        /// <returns>Elements in the same grid cell</returns>
        IEnumerable<object> GetElementsInSameCell(SpatialGrid spatialGrid, object targetElement);

        /// <summary>
        /// Gets elements in adjacent grid cells
        /// </summary>
        /// <param name="spatialGrid">Spatial grid</param>
        /// <param name="targetElement">Target element</param>
        /// <param name="cellRadius">Number of adjacent cells to include</param>
        /// <returns>Elements in adjacent cells</returns>
        IEnumerable<object> GetElementsInAdjacentCells(SpatialGrid spatialGrid, object targetElement, int cellRadius = 1);
    }

    /// <summary>
    /// Represents a spatial index for efficient proximity searches
    /// </summary>
    public class SpatialIndex
    {
        public Dictionary<string, List<FireStoppingElement>> FireStoppingGrid { get; set; } = new();
        public Dictionary<string, List<StructuralElement>> StructuralGrid { get; set; } = new();
        public Dictionary<string, List<ServiceElement>> ServiceGrid { get; set; } = new();
        public double GridSize { get; set; } = 10.0; // feet
        public BoundingBoxXYZ OverallBounds { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Represents the result of a spatial search
    /// </summary>
    public class SpatialSearchResult
    {
        public List<FireStoppingElement> NearbyFireStoppingElements { get; set; } = new();
        public List<StructuralElement> NearbyStructuralElements { get; set; } = new();
        public List<ServiceElement> NearbyServiceElements { get; set; } = new();
        public double SearchDistance { get; set; }
        public XYZ SearchCenter { get; set; }
        public int TotalElementsFound => NearbyFireStoppingElements.Count + NearbyStructuralElements.Count + NearbyServiceElements.Count;
    }

    /// <summary>
    /// Represents a spatial grid for element partitioning
    /// </summary>
    public class SpatialGrid
    {
        public Dictionary<string, List<object>> Grid { get; set; } = new();
        public double CellSize { get; set; }
        public BoundingBoxXYZ Bounds { get; set; }
        public int GridWidth { get; set; }
        public int GridHeight { get; set; }
        public int GridDepth { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// Gets the grid key for a given point
        /// </summary>
        /// <param name="point">Point to get key for</param>
        /// <returns>Grid key string</returns>
        public string GetGridKey(XYZ point)
        {
            if (Bounds == null) return "0,0,0";

            var x = (int)Math.Floor((point.X - Bounds.Min.X) / CellSize);
            var y = (int)Math.Floor((point.Y - Bounds.Min.Y) / CellSize);
            var z = (int)Math.Floor((point.Z - Bounds.Min.Z) / CellSize);

            // Clamp to grid bounds
            x = Math.Max(0, Math.Min(GridWidth - 1, x));
            y = Math.Max(0, Math.Min(GridHeight - 1, y));
            z = Math.Max(0, Math.Min(GridDepth - 1, z));

            return $"{x},{y},{z}";
        }

        /// <summary>
        /// Gets adjacent grid keys within a specified radius
        /// </summary>
        /// <param name="centerKey">Center grid key</param>
        /// <param name="radius">Radius in grid cells</param>
        /// <returns>Adjacent grid keys</returns>
        public IEnumerable<string> GetAdjacentKeys(string centerKey, int radius = 1)
        {
            var keys = new List<string>();
            var parts = centerKey.Split(',');
            
            if (parts.Length != 3) return keys;

            if (!int.TryParse(parts[0], out var centerX) ||
                !int.TryParse(parts[1], out var centerY) ||
                !int.TryParse(parts[2], out var centerZ))
            {
                return keys;
            }

            for (int x = centerX - radius; x <= centerX + radius; x++)
            {
                for (int y = centerY - radius; y <= centerY + radius; y++)
                {
                    for (int z = centerZ - radius; z <= centerZ + radius; z++)
                    {
                        if (x >= 0 && x < GridWidth &&
                            y >= 0 && y < GridHeight &&
                            z >= 0 && z < GridDepth)
                        {
                            keys.Add($"{x},{y},{z}");
                        }
                    }
                }
            }

            return keys;
        }
    }
}
