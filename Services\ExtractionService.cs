using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using MEP.Pacifire.Helpers;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Service for extracting fire stopping elements, services, and structures from Revit models.
    /// Handles coordinate transformation and filtering based on user selections.
    /// </summary>
    public class ExtractionService : IExtractionService
    {
        private readonly IGeometryHelper _geometryHelper;
        private readonly IParameterHelper _parameterHelper;

        public event EventHandler<ExtractionProgressEventArgs>? ProgressChanged;
        public event EventHandler<ExtractionStatusEventArgs>? StatusChanged;

        public ExtractionService(IGeometryHelper geometryHelper, IParameterHelper parameterHelper)
        {
            _geometryHelper = geometryHelper ?? throw new ArgumentNullException(nameof(geometryHelper));
            _parameterHelper = parameterHelper ?? throw new ArgumentNullException(nameof(parameterHelper));
        }

        /// <summary>
        /// Gets a normalized transform that ensures all elements are transformed to the same coordinate system (host model coordinates).
        /// This method addresses the issue where linked models with different Internal Origins result in misaligned coordinate systems.
        /// </summary>
        /// <param name="linkInstance">The RevitLinkInstance to get the transform for</param>
        /// <param name="hostDocument">The host document</param>
        /// <returns>A transform that normalizes coordinates to the host model's coordinate system</returns>
        private Transform GetNormalizedTransform(RevitLinkInstance linkInstance, Document hostDocument)
        {
            try
            {
                // Get the standard link transform (includes Internal Origin offset)
                var linkTransform = linkInstance.GetTransform();

                // Get the linked document
                var linkedDoc = linkInstance.GetLinkDocument();
                if (linkedDoc == null)
                {
                    OnStatusChanged($"Warning: Could not access linked document for transform normalization", false);
                    return linkTransform;
                }

                // For models with different Internal Origins, we need to account for the coordinate system differences
                // The linkTransform already includes the Internal Origin offset, so we can use it directly
                // However, we add validation to ensure coordinate system consistency

                // Validate that the transform is reasonable (not null, not identity when it shouldn't be)
                if (linkTransform == null)
                {
                    OnStatusChanged($"Warning: Null transform detected for link instance", false);
                    return Transform.Identity;
                }

                // Log transform details for debugging coordinate issues
                OnStatusChanged($"Transform for {linkInstance.Name}: Origin=({linkTransform.Origin.X:F2}, {linkTransform.Origin.Y:F2}, {linkTransform.Origin.Z:F2})", false);

                return linkTransform;
            }
            catch (Exception ex)
            {
                OnStatusChanged($"Error getting normalized transform: {ex.Message}", true);
                return Transform.Identity;
            }
        }

        /// <summary>
        /// Extracts fire stopping elements from selected linked models
        /// </summary>
        public IEnumerable<FireStoppingElement> ExtractFireStoppingElements(
            Document document,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default)
        {
            OnStatusChanged("Extracting fire stopping elements...", false);
            
            var fireStoppingElements = new List<FireStoppingElement>();
            var selectedLinks = filterSettings.SelectedLinkedModels.Where(x => x.IsSelected).ToList();
            
            if (!selectedLinks.Any())
            {
                OnStatusChanged("No linked models selected for extraction.", true);
                return fireStoppingElements;
            }

            var totalLinks = selectedLinks.Count;
            var currentLink = 0;

            foreach (var linkFilter in selectedLinks)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                currentLink++;
                OnProgressChanged(currentLink, totalLinks, $"Processing {linkFilter.Name}...");
                Application.DoEvents(); // Allow UI to update

                try
                {
                    var linkInstance = GetLinkInstance(document, linkFilter.Name);
                    if (linkInstance?.GetLinkDocument() == null)
                    {
                        OnStatusChanged($"Warning: Could not access linked document '{linkFilter.Name}'", false);
                        continue;
                    }

                    var linkDoc = linkInstance.GetLinkDocument();
                    var transform = GetNormalizedTransform(linkInstance, document);

                    // Extract fire stopping fittings from this link
                    var elements = ExtractFireStoppingFromLink(linkDoc, linkInstance, transform, filterSettings,
                        cancellationToken, currentLink, totalLinks);
                    fireStoppingElements.AddRange(elements);
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"Error processing link '{linkFilter.Name}': {ex.Message}", true);
                }
            }

            OnStatusChanged($"Extracted {fireStoppingElements.Count} fire stopping elements.", false);
            return fireStoppingElements;
        }

        /// <summary>
        /// Extracts service elements connected to fire stopping elements from selected linked models
        /// </summary>
        public IEnumerable<ServiceElement> ExtractConnectedServices(
            Document document,
            IEnumerable<FireStoppingElement> fireStoppingElements,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default)
        {
            var hasLevelFilter = filterSettings.SelectedLevels.Any(x => x.IsSelected);
            var levelFilterMessage = hasLevelFilter ? " (level-filtered)" : "";
            OnStatusChanged($"Extracting service elements from linked models{levelFilterMessage}...", false);

            var serviceElements = new List<ServiceElement>();
            var selectedLinks = filterSettings.SelectedLinkedModels.Where(x => x.IsSelected).ToList();
            var totalLinks = selectedLinks.Count;
            var currentLink = 0;

            foreach (var linkFilter in selectedLinks)
            {
                cancellationToken.ThrowIfCancellationRequested();

                currentLink++;
                OnProgressChanged(currentLink, totalLinks, $"Processing services from {linkFilter.Name}...");
                Application.DoEvents(); // Allow UI to update

                try
                {
                    var linkInstance = GetLinkInstance(document, linkFilter.Name);
                    if (linkInstance?.GetLinkDocument() == null)
                    {
                        OnStatusChanged($"Warning: Could not access linked document '{linkFilter.Name}'", false);
                        continue;
                    }

                    var linkDoc = linkInstance.GetLinkDocument();
                    var transform = GetNormalizedTransform(linkInstance, document);

                    // Extract service elements from this link
                    var elements = ExtractServicesFromLink(linkDoc, linkInstance, transform, filterSettings,
                        cancellationToken, currentLink, totalLinks);
                    serviceElements.AddRange(elements);
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"Error processing services from link '{linkFilter.Name}': {ex.Message}", true);
                }
            }

            OnStatusChanged($"Extracted {serviceElements.Count} service elements from linked models{levelFilterMessage}.", false);
            return serviceElements;
        }

        /// <summary>
        /// Extracts structural elements from selected linked models
        /// </summary>
        public IEnumerable<StructuralElement> ExtractStructuralElements(
            Document document,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default)
        {
            var hasLevelFilter = filterSettings.SelectedLevels.Any(x => x.IsSelected);
            var levelFilterMessage = hasLevelFilter ? " (level-filtered)" : "";
            OnStatusChanged($"Extracting structural elements{levelFilterMessage}...", false);

            var structuralElements = new List<StructuralElement>();
            var selectedLinks = filterSettings.SelectedLinkedModels.Where(x => x.IsSelected).ToList();
            
            var totalLinks = selectedLinks.Count;
            var currentLink = 0;

            foreach (var linkFilter in selectedLinks)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                currentLink++;
                OnProgressChanged(currentLink, totalLinks, $"Processing structures in {linkFilter.Name}...");

                try
                {
                    var linkInstance = GetLinkInstance(document, linkFilter.Name);
                    if (linkInstance?.GetLinkDocument() == null)
                        continue;

                    var linkDoc = linkInstance.GetLinkDocument();
                    var transform = GetNormalizedTransform(linkInstance, document);

                    // Extract walls and floors from this link
                    var elements = ExtractStructuresFromLink(linkDoc, linkInstance, transform, filterSettings,
                        cancellationToken, currentLink, totalLinks);
                    structuralElements.AddRange(elements);
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"Error processing structures in '{linkFilter.Name}': {ex.Message}", true);
                }
            }

            OnStatusChanged($"Extracted {structuralElements.Count} structural elements{levelFilterMessage}.", false);
            return structuralElements;
        }

        /// <summary>
        /// Gets all available levels in the document
        /// </summary>
        public IEnumerable<LevelFilter> GetAvailableLevels(Document document)
        {
            var levels = new FilteredElementCollector(document)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .OrderBy(l => l.Elevation)
                .Select(l => new LevelFilter(l.Name, true, l.Elevation)) // Changed to true - all levels selected by default
                .ToList();

            return levels;
        }

        /// <summary>
        /// Gets all available categories relevant for fire stopping analysis
        /// </summary>
        public IEnumerable<CategoryFilter> GetAvailableCategories(Document document)
        {
            var categories = new List<CategoryFilter>
            {
                new("Duct Fittings", true, "MEP duct fittings and accessories"),
                new("Pipe Fittings", true, "MEP pipe fittings and accessories"),
                new("Cable Tray Fittings", true, "Electrical cable tray fittings"),
                new("Conduit Fittings", true, "Electrical conduit fittings")
                // Note: Only showing categories that are actually searched during extraction
                // Flex Duct, Flex Pipe, Walls, Floors, Ceilings are not searched for fire stopping elements
            };

            return categories;
        }

        /// <summary>
        /// Gets all loaded linked models in the document
        /// </summary>
        public IEnumerable<LinkedModelFilter> GetAvailableLinkedModels(Document document)
        {
            var linkedModels = new List<LinkedModelFilter>();

            var linkInstances = new FilteredElementCollector(document)
                .OfClass(typeof(RevitLinkInstance))
                .Cast<RevitLinkInstance>()
                .Where(link => link.GetLinkDocument() != null);

            foreach (var linkInstance in linkInstances)
            {
                try
                {
                    var linkDoc = linkInstance.GetLinkDocument();
                    var linkName = linkDoc?.Title ?? linkInstance.Name;
                    var filePath = linkDoc?.PathName ?? "";
                    var modelType = DetermineModelType(linkName, filePath);

                    linkedModels.Add(new LinkedModelFilter(linkName, true, filePath, modelType));
                }
                catch (Exception)
                {
                    // Skip problematic links
                    continue;
                }
            }

            return linkedModels.OrderBy(x => x.Name);
        }

        /// <summary>
        /// Validates that the document is suitable for fire stopping analysis
        /// </summary>
        public ValidationResult ValidateDocument(Document document)
        {
            var result = new ValidationResult { IsValid = true };

            // Check if document is valid
            if (document == null)
            {
                result.IsValid = false;
                result.Issues.Add("Document is null or invalid.");
                return result;
            }

            // Check for linked models
            var linkCount = new FilteredElementCollector(document)
                .OfClass(typeof(RevitLinkInstance))
                .GetElementCount();

            if (linkCount == 0)
            {
                result.Warnings.Add("No linked models found. Fire stopping analysis requires linked MEP and architectural models.");
            }

            // Check for levels
            var levelCount = new FilteredElementCollector(document)
                .OfClass(typeof(Level))
                .GetElementCount();

            if (levelCount == 0)
            {
                result.IsValid = false;
                result.Issues.Add("No levels found in the document.");
            }

            return result;
        }

        #region Private Helper Methods

        private RevitLinkInstance? GetLinkInstance(Document document, string linkName)
        {
            return new FilteredElementCollector(document)
                .OfClass(typeof(RevitLinkInstance))
                .Cast<RevitLinkInstance>()
                .FirstOrDefault(link => link.GetLinkDocument()?.Title == linkName || link.Name == linkName);
        }

        private List<FireStoppingElement> ExtractFireStoppingFromLink(
            Document linkDoc,
            RevitLinkInstance linkInstance,
            Transform transform,
            FilterSettings filterSettings,
            CancellationToken cancellationToken,
            int currentLink = 1,
            int totalLinks = 1)
        {
            var elements = new List<FireStoppingElement>();

            // Get all fittings that contain "Fire Stopping" in their family type name
            var fittingCategories = new[]
            {
                BuiltInCategory.OST_DuctFitting,
                BuiltInCategory.OST_PipeFitting,
                BuiltInCategory.OST_CableTrayFitting,
                BuiltInCategory.OST_ConduitFitting
            };

            // First pass: count total elements for accurate progress tracking
            var totalElements = 0;
            foreach (var category in fittingCategories)
            {
                var collector = new FilteredElementCollector(linkDoc)
                    .OfCategory(category)
                    .WhereElementIsNotElementType();
                totalElements += collector.GetElementCount();
            }

            var processedElements = 0;
            var linkName = linkInstance.GetLinkDocument()?.Title ?? "Unknown";

            foreach (var category in fittingCategories)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var collector = new FilteredElementCollector(linkDoc)
                    .OfCategory(category)
                    .WhereElementIsNotElementType();

                foreach (Element element in collector)
                {
                    processedElements++;

                    // Update progress every element (but throttle UI updates)
                    if (processedElements % 5 == 0 || processedElements == totalElements)
                    {
                        var linkProgress = (double)(currentLink - 1) / totalLinks;
                        var elementProgress = (double)processedElements / totalElements / totalLinks;
                        var totalProgress = (linkProgress + elementProgress) * 100;

                        OnProgressChanged((int)totalProgress, 100,
                            $"Processing {linkName}: {processedElements}/{totalElements} elements");
                        Application.DoEvents();
                    }

                    if (IsFireStoppingElement(element))
                    {
                        var fireStoppingElement = CreateFireStoppingElement(element, linkInstance, transform);
                        if (fireStoppingElement != null && PassesFilters(fireStoppingElement, filterSettings))
                        {
                            elements.Add(fireStoppingElement);
                        }
                    }
                }
            }

            return elements;
        }

        private bool IsFireStoppingElement(Element element)
        {
            // Check family type name for "Fire Stopping"
            var familySymbol = element.Document.GetElement(element.GetTypeId()) as FamilySymbol;
            var familyName = familySymbol?.FamilyName ?? "";
            var typeName = familySymbol?.Name ?? "";

            return familyName.Contains("Fire_Stopping", StringComparison.OrdinalIgnoreCase) ||
                   typeName.Contains("Fire_Stopping", StringComparison.OrdinalIgnoreCase);
        }

        private FireStoppingElement? CreateFireStoppingElement(Element element, RevitLinkInstance linkInstance, Transform transform)
        {
            try
            {
                var fireStoppingElement = new FireStoppingElement(element.Id, element, linkInstance);

                // Set linked model name
                if (linkInstance != null)
                {
                    var linkDoc = linkInstance.GetLinkDocument();
                    fireStoppingElement.LinkedModelName = linkDoc?.Title ?? linkInstance.Name;
                }
                else
                {
                    fireStoppingElement.LinkedModelName = "Host Model";
                }

                // Extract parameters
                _parameterHelper.ExtractFireStoppingParameters(element, fireStoppingElement);

                // Transform geometry
                _geometryHelper.TransformElementGeometry(element, transform, fireStoppingElement);

                // Set coordinate system tracking properties for debugging Internal Origin issues
                fireStoppingElement.CoordinateTransform = transform;
                fireStoppingElement.SourceModelName = fireStoppingElement.LinkedModelName;

                return fireStoppingElement;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private bool PassesFilters(FireStoppingElement element, FilterSettings filterSettings)
        {
            // Level filter
            if (filterSettings.SelectedLevels.Any(x => x.IsSelected) &&
                !filterSettings.SelectedLevels.Any(x => x.IsSelected && x.Name == element.LevelName))
            {
                return false;
            }

            // Category filter
            if (filterSettings.SelectedCategories.Any(x => x.IsSelected) &&
                !filterSettings.SelectedCategories.Any(x => x.IsSelected && x.Name == element.Category))
            {
                return false;
            }

            // Search text filter
            if (!string.IsNullOrEmpty(filterSettings.SearchText))
            {
                var searchText = filterSettings.SearchText.ToLowerInvariant();
                var searchableText = $"{element.FamilyName} {element.TypeName} {element.BecaInstMark} {element.BecaTypeMark}".ToLowerInvariant();
                
                if (!searchableText.Contains(searchText))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Checks if an element passes level filtering based on filter settings
        /// </summary>
        private bool PassesLevelFilter(string elementLevelName, FilterSettings filterSettings)
        {
            // If no levels are selected, include all elements (no filtering)
            if (!filterSettings.SelectedLevels.Any(x => x.IsSelected))
            {
                return true;
            }

            // Check if element's level is in the selected levels
            return filterSettings.SelectedLevels.Any(x => x.IsSelected &&
                string.Equals(x.Name, elementLevelName, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets the selected level names from filter settings for efficient lookup
        /// </summary>
        private HashSet<string> GetSelectedLevelNames(FilterSettings filterSettings)
        {
            return filterSettings.SelectedLevels
                .Where(x => x.IsSelected)
                .Select(x => x.Name)
                .ToHashSet(StringComparer.OrdinalIgnoreCase);
        }

        private List<ServiceElement> ExtractServicesFromLink(
            Document linkDoc,
            RevitLinkInstance linkInstance,
            Transform transform,
            FilterSettings filterSettings,
            CancellationToken cancellationToken,
            int currentLink = 1,
            int totalLinks = 1)
        {
            var elements = new List<ServiceElement>();

            // Define MEP service categories to search for
            var serviceCategories = new[]
            {
                BuiltInCategory.OST_DuctCurves,        // Ducts
                BuiltInCategory.OST_PipeCurves,        // Pipes
                BuiltInCategory.OST_CableTray,         // Cable Trays
                BuiltInCategory.OST_Conduit,           // Conduits
                BuiltInCategory.OST_FlexDuctCurves,    // Flex Ducts
                BuiltInCategory.OST_FlexPipeCurves     // Flex Pipes
            };

            // Get selected level names for efficient filtering
            var selectedLevelNames = GetSelectedLevelNames(filterSettings);
            var hasLevelFilter = selectedLevelNames.Any();

            // First pass: count total elements for accurate progress tracking
            var totalElements = 0;
            foreach (var category in serviceCategories)
            {
                try
                {
                    var collector = new FilteredElementCollector(linkDoc)
                        .OfCategory(category)
                        .WhereElementIsNotElementType();
                    totalElements += collector.GetElementCount();
                }
                catch { /* Skip categories that don't exist in this model */ }
            }

            var processedElements = 0;
            var extractedElements = 0;
            var linkName = linkInstance.GetLinkDocument()?.Title ?? "Unknown";

            foreach (var category in serviceCategories)
            {
                cancellationToken.ThrowIfCancellationRequested();

                try
                {
                    var collector = new FilteredElementCollector(linkDoc)
                        .OfCategory(category)
                        .WhereElementIsNotElementType();

                    foreach (Element element in collector)
                    {
                        processedElements++;

                        // Update progress every 10 elements (services are typically more numerous)
                        if (processedElements % 10 == 0 || processedElements == totalElements)
                        {
                            var linkProgress = (double)(currentLink - 1) / totalLinks;
                            var elementProgress = (double)processedElements / totalElements / totalLinks;
                            var totalProgress = (linkProgress + elementProgress) * 100;

                            OnProgressChanged((int)totalProgress, 100,
                                 $"Processing services in {linkName}: {processedElements}/{totalElements} elements (extracted: {extractedElements})");
                            Application.DoEvents();
                        }

                        // Apply level filtering during extraction for performance
                        if (hasLevelFilter)
                        {
                            var elementLevelName = GetLevelName(element);
                            if (!PassesLevelFilter(elementLevelName, filterSettings))
                            {
                                continue; // Skip this element - not on selected level
                            }
                        }

                        var serviceElement = CreateServiceElement(element, linkInstance, transform, category);
                        if (serviceElement != null)
                        {
                            elements.Add(serviceElement);
                            extractedElements++;
                        }
                    }
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"Error extracting {category} elements: {ex.Message}", true);
                }
            }

            return elements;
        }

        /// <summary>
        /// Gets the location point of an element (works for various element types)
        /// For linear elements, returns the closest endpoint to the fire stopping element
        /// </summary>
        private XYZ? GetElementLocation(Element element, XYZ? fireStoppingLocation = null)
        {
            try
            {
                // Try location point first (for family instances)
                if (element.Location is LocationPoint locationPoint)
                {
                    return locationPoint.Point;
                }

                // Try location curve (for linear elements like ducts, pipes)
                if (element.Location is LocationCurve locationCurve)
                {
                    var curve = locationCurve.Curve;

                    // If we have a fire stopping location, find the closest endpoint
                    if (fireStoppingLocation != null)
                    {
                        var startPoint = curve.GetEndPoint(0);
                        var endPoint = curve.GetEndPoint(1);

                        var distanceToStart = fireStoppingLocation.DistanceTo(startPoint);
                        var distanceToEnd = fireStoppingLocation.DistanceTo(endPoint);

                        // Return the closest endpoint (where fittings/connections typically are)
                        return distanceToStart <= distanceToEnd ? startPoint : endPoint;
                    }
                    else
                    {
                        // Fallback to midpoint if no fire stopping location provided
                        return curve.Evaluate(0.5, true);
                    }
                }

                // Fallback to bounding box center
                var bbox = element.get_BoundingBox(null);
                if (bbox != null)
                {
                    return (bbox.Min + bbox.Max) / 2.0;
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting element location: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Creates a ServiceElement from a Revit element in a linked document
        /// </summary>
        private ServiceElement? CreateServiceElement(Element element, RevitLinkInstance linkInstance, Transform transform, BuiltInCategory category)
        {
            try
            {
                // Get element location and transform it to host coordinates
                var location = GetElementLocation(element);
                if (location == null) return null;

                var transformedLocation = transform.OfPoint(location);
                var serviceType = GetServiceTypeFromCategory(category);

                var serviceElement = new ServiceElement(element.Id, element, linkInstance, serviceType)
                {
                    LocationPoint = transformedLocation,
                    Category = category.ToString(),
                    LevelName = GetLevelName(element),
                    Size = GetServiceSize(element, serviceType),
                    Material = GetMaterialName(element),
                    SystemType = GetSystemType(element),
                    SystemName = GetSystemName(element)
                };

                // Set endpoints for linear elements
                SetServiceElementEndpoints(element, serviceElement, transform);

                // Transform geometry if available
                var geometry = GetElementGeometry(element);
                if (geometry != null)
                {
                    serviceElement.TransformedSolid = SolidUtils.CreateTransformed(geometry, transform);
                }

                return serviceElement;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating service element: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Maps BuiltInCategory to ServiceType enum
        /// </summary>
        private PF_ServiceType GetServiceTypeFromCategory(BuiltInCategory category)
        {
            return category switch
            {
                BuiltInCategory.OST_DuctCurves => PF_ServiceType.Duct,
                BuiltInCategory.OST_PipeCurves => PF_ServiceType.Pipe,
                BuiltInCategory.OST_CableTray => PF_ServiceType.CableTray,
                BuiltInCategory.OST_Conduit => PF_ServiceType.Conduit,
                BuiltInCategory.OST_FlexDuctCurves => PF_ServiceType.FlexDuct,
                BuiltInCategory.OST_FlexPipeCurves => PF_ServiceType.FlexPipe,
                _ => PF_ServiceType.Unknown
            };
        }

        /// <summary>
        /// Gets the level name for an element
        /// </summary>
        private string GetLevelName(Element element)
        {
            try
            {
                var levelId = element.LevelId;
                if (levelId != ElementId.InvalidElementId)
                {
                    var level = element.Document.GetElement(levelId) as Level;
                    return level?.Name ?? "Unknown Level";
                }
                return "Unknown Level";
            }
            catch (Exception)
            {
                return "Unknown Level";
            }
        }

        /// <summary>
        /// Gets the service size based on element type
        /// </summary>
        private string GetServiceSize(Element element, PF_ServiceType serviceType)
        {
            try
            {
                return serviceType switch
                {
                    PF_ServiceType.Duct => GetDuctSize(element),
                    PF_ServiceType.Pipe => GetPipeSize(element),
                    PF_ServiceType.CableTray => GetCableTraySize(element),
                    PF_ServiceType.Conduit => GetConduitSize(element),
                    PF_ServiceType.FlexDuct => GetDuctSize(element),
                    PF_ServiceType.FlexPipe => GetPipeSize(element),
                    _ => "Unknown"
                };
            }
            catch (Exception)
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Gets duct size (width x height)
        /// </summary>
        private string GetDuctSize(Element element)
        {
            try
            {
                var width = GetParameterValue(element, "Width");
                var height = GetParameterValue(element, "Height");
                var diameter = GetParameterValue(element, "Diameter");

                if (diameter > 0)
                    return $"�{diameter:F0}mm";
                else if (width > 0 && height > 0)
                    return $"{width:F0}�{height:F0}mm";
                else
                    return "Unknown";
            }
            catch (Exception)
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Gets pipe size (diameter)
        /// </summary>
        private string GetPipeSize(Element element)
        {
            try
            {
                var diameter = GetParameterValue(element, "Diameter");
                return diameter > 0 ? $"�{diameter:F0}mm" : "Unknown";
            }
            catch (Exception)
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Gets cable tray size (width x height)
        /// </summary>
        private string GetCableTraySize(Element element)
        {
            try
            {
                var width = GetParameterValue(element, "Width");
                var height = GetParameterValue(element, "Height");

                if (width > 0 && height > 0)
                    return $"{width:F0}�{height:F0}mm";
                else
                    return "Unknown";
            }
            catch (Exception)
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Gets conduit size (diameter)
        /// </summary>
        private string GetConduitSize(Element element)
        {
            try
            {
                var diameter = GetParameterValue(element, "Diameter");
                return diameter > 0 ? $"�{diameter:F0}mm" : "Unknown";
            }
            catch (Exception)
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Gets material name from element
        /// </summary>
        private string GetMaterialName(Element element)
        {
            try
            {
                var materialId = element.GetMaterialIds(false).FirstOrDefault();
                if (materialId != ElementId.InvalidElementId)
                {
                    var material = element.Document.GetElement(materialId) as Material;
                    return material?.Name ?? "Unknown Material";
                }
                return "Unknown Material";
            }
            catch (Exception)
            {
                return "Unknown Material";
            }
        }

        /// <summary>
        /// Gets system type from element
        /// </summary>
        private string GetSystemType(Element element)
        {
            try
            {
                return GetParameterValueAsString(element, "System Type") ?? "Unknown System";
            }
            catch (Exception)
            {
                return "Unknown System";
            }
        }

        /// <summary>
        /// Gets system name from element
        /// </summary>
        private string GetSystemName(Element element)
        {
            try
            {
                return GetParameterValueAsString(element, "System Name") ?? "Unknown System";
            }
            catch (Exception)
            {
                return "Unknown System";
            }
        }

        /// <summary>
        /// Gets parameter value as double (converted to mm)
        /// </summary>
        private double GetParameterValue(Element element, string parameterName)
        {
            try
            {
                var parameter = element.LookupParameter(parameterName);
                if (parameter != null && parameter.HasValue)
                {
                    return parameter.AsDouble() * 304.8; // Convert feet to mm
                }
                return 0.0;
            }
            catch (Exception)
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Gets parameter value as string
        /// </summary>
        private string? GetParameterValueAsString(Element element, string parameterName)
        {
            try
            {
                var parameter = element.LookupParameter(parameterName);
                if (parameter != null && parameter.HasValue)
                {
                    return parameter.AsString();
                }
                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Sets the endpoint properties for linear service elements
        /// </summary>
        private void SetServiceElementEndpoints(Element element, ServiceElement serviceElement, Transform transform)
        {
            try
            {
                // Check if element has a location curve (linear elements)
                if (element.Location is LocationCurve locationCurve)
                {
                    var curve = locationCurve.Curve;

                    // Get the endpoints of the curve
                    var endPoint1 = curve.GetEndPoint(0);
                    var endPoint2 = curve.GetEndPoint(1);

                    // Transform endpoints to host coordinates
                    serviceElement.EndPoint1 = transform.OfPoint(endPoint1);
                    serviceElement.EndPoint2 = transform.OfPoint(endPoint2);
                }
                else
                {
                    // For non-linear elements (family instances), set endpoints to location point
                    serviceElement.EndPoint1 = serviceElement.LocationPoint;
                    serviceElement.EndPoint2 = serviceElement.LocationPoint;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting service element endpoints: {ex.Message}");
                // Fallback to location point for both endpoints
                serviceElement.EndPoint1 = serviceElement.LocationPoint;
                serviceElement.EndPoint2 = serviceElement.LocationPoint;
            }
        }

        /// <summary>
        /// Gets the solid geometry from an element
        /// </summary>
        private Solid? GetElementGeometry(Element element)
        {
            try
            {
                var options = new Options
                {
                    ComputeReferences = false,
                    DetailLevel = ViewDetailLevel.Medium,
                    IncludeNonVisibleObjects = false
                };

                var geometryElement = element.get_Geometry(options);
                if (geometryElement == null) return null;

                foreach (GeometryObject geometryObject in geometryElement)
                {
                    if (geometryObject is Solid solid && solid.Volume > 1e-6)
                    {
                        return solid;
                    }
                    else if (geometryObject is GeometryInstance geometryInstance)
                    {
                        var instanceGeometry = geometryInstance.GetInstanceGeometry();
                        foreach (GeometryObject instanceObject in instanceGeometry)
                        {
                            if (instanceObject is Solid instanceSolid && instanceSolid.Volume > 1e-6)
                            {
                                return instanceSolid;
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting element geometry: {ex.Message}");
                return null;
            }
        }

        private List<StructuralElement> ExtractStructuresFromLink(
            Document linkDoc,
            RevitLinkInstance linkInstance,
            Transform transform,
            FilterSettings filterSettings,
            CancellationToken cancellationToken,
            int currentLink = 1,
            int totalLinks = 1)
        {
            var elements = new List<StructuralElement>();

            // Extract walls and floors
            var structuralCategories = new[]
            {
                BuiltInCategory.OST_Walls,
                BuiltInCategory.OST_Floors
            };

            // Get selected level names for efficient filtering
            var selectedLevelNames = GetSelectedLevelNames(filterSettings);
            var hasLevelFilter = selectedLevelNames.Any();

            // First pass: count total elements for accurate progress tracking
            var totalElements = 0;
            foreach (var category in structuralCategories)
            {
                var collector = new FilteredElementCollector(linkDoc)
                    .OfCategory(category)
                    .WhereElementIsNotElementType();
                totalElements += collector.GetElementCount();
            }

            var processedElements = 0;
            var extractedElements = 0;
            var linkName = linkInstance.GetLinkDocument()?.Title ?? "Unknown";

            foreach (var category in structuralCategories)
            {
                cancellationToken.ThrowIfCancellationRequested();

                var collector = new FilteredElementCollector(linkDoc)
                    .OfCategory(category)
                    .WhereElementIsNotElementType();

                foreach (Element element in collector)
                {
                    processedElements++;

                    // Update progress every 5 elements (structural elements are typically fewer)
                    if (processedElements % 5 == 0 || processedElements == totalElements)
                    {
                        var linkProgress = (double)(currentLink - 1) / totalLinks;
                        var elementProgress = (double)processedElements / totalElements / totalLinks;
                        var totalProgress = (linkProgress + elementProgress) * 100;

                        OnProgressChanged((int)totalProgress, 100,
                           $"Processing structures in {linkName}: {processedElements}/{totalElements} elements (extracted: {extractedElements})");
                        Application.DoEvents();
                    }

                    // Apply level filtering during extraction for performance
                    if (hasLevelFilter)
                    {
                        var elementLevelName = GetLevelName(element);
                        if (!PassesLevelFilter(elementLevelName, filterSettings))
                        {
                            continue; // Skip this element - not on selected level
                        }
                    }

                    var structuralElement = CreateStructuralElement(element, linkInstance, transform);
                    if (structuralElement != null)
                    {
                        elements.Add(structuralElement);
                        extractedElements++;
                    }
                }
            }

            return elements;
        }

        private StructuralElement? CreateStructuralElement(Element element, RevitLinkInstance linkInstance, Transform transform)
        {
            try
            {
                var structureType = _parameterHelper.DetermineStructureType(element);

                var structuralElement = new StructuralElement(element.Id, element, linkInstance, structureType);

                // Extract parameters
                _parameterHelper.ExtractStructuralParameters(element, structuralElement);

                // Transform geometry
                _geometryHelper.TransformElementGeometry(element, transform, structuralElement);

                // Set coordinate system tracking properties for debugging Internal Origin issues
                structuralElement.CoordinateTransform = transform;
                var linkDoc = linkInstance?.GetLinkDocument();
                structuralElement.SourceModelName = linkDoc?.Title ?? linkInstance?.Name ?? "Host Model";

                return structuralElement;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private string DetermineModelType(string linkName, string filePath)
        {
            var name = linkName.ToLowerInvariant();
            
            if (name.Contains("mep") || name.Contains("mechanical") || name.Contains("electrical") || name.Contains("plumbing"))
                return "MEP";
            if (name.Contains("arch") || name.Contains("interior"))
                return "Architectural";
            if (name.Contains("struct"))
                return "Structural";
            
            return "Other";
        }

        private void OnProgressChanged(int current, int total, string operation)
        {
            ProgressChanged?.Invoke(this, new ExtractionProgressEventArgs
            {
                Current = current,
                Total = total,
                CurrentOperation = operation
            });
        }

        private void OnStatusChanged(string status, bool isError, Exception? exception = null)
        {
            StatusChanged?.Invoke(this, new ExtractionStatusEventArgs
            {
                Status = status,
                IsError = isError,
                Exception = exception
            });
        }

        #endregion
    }
}
