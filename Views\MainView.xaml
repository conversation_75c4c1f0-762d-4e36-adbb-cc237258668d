<Window
    x:Class="MEP.Pacifire.Views.MainView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:converters="clr-namespace:MEP.Pacifire.Converters"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="Beca Tools | PFS"
    Width="1400"
    Height="850"
    WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <!--  Converters  -->
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter" />
            <converters:InverseBooleanToVisibilityConverter x:Key="InverseBooleanToVisibilityConverter" />
            <converters:BooleanToPassFailConverter x:Key="BooleanToPassFailConverter" />
            <converters:BooleanToColorConverter x:Key="BooleanToColorConverter" />
            <converters:SuccessRateConverter x:Key="SuccessRateConverter" />
            <converters:ElementCountConverter x:Key="ElementCountConverter" />
            <converters:DistanceConverter x:Key="DistanceConverter" />
            <converters:PercentageConverter x:Key="PercentageConverter" />
        </ResourceDictionary>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>

            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  Header  -->
        <TextBlock
            Grid.Row="0"
            Grid.ColumnSpan="5"
            Margin="15,5,0,10"
            Style="{StaticResource MaterialDesignHeadline4TextBlock}"
            Text="PACIFIRE - Passive Fire Stopping Solution Exporter" />
        <Button
            Grid.Column="6"
            Height="45"
            HorizontalAlignment="Right"
            VerticalAlignment="Center"
            Background="Transparent"
            BorderBrush="Transparent"
            Command="{Binding OpenDocumentationCommand}"
            Content="{materialDesign:PackIcon Kind=HelpCircleOutline,
                                              Size=38}"
            Foreground="#12A8B2" />
        <Separator
            Grid.ColumnSpan="6"
            Margin="10,45,15,0"
            Background="#FFCE00">
            <Separator.LayoutTransform>
                <ScaleTransform ScaleY="3.5" />
            </Separator.LayoutTransform>
        </Separator>

        <!--  Main Content  -->
        <Grid Grid.Row="1" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!--  Sidebar  -->
            <Expander
                x:Name="exp_SideBar"
                Margin="10,4,8,8"
                BorderBrush="#12A8B2"
                BorderThickness="1"
                ExpandDirection="Right">
                <Expander.Header>
                    <Border>
                        <TextBlock
                            FontSize="15"
                            RenderTransformOrigin=".5,.5"
                            Text="FILTER  -  ACTION">
                            <TextBlock.LayoutTransform>
                                <RotateTransform Angle="270" />
                            </TextBlock.LayoutTransform>
                        </TextBlock>
                    </Border>
                </Expander.Header>

                <Grid>
                    <Grid.Style>
                        <Style TargetType="Grid">
                            <Setter Property="Width" Value="320" />
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsSidebarCollapsed}" Value="True">
                                    <Setter Property="Width" Value="64" />
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </Grid.Style>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="*" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>

                    <StackPanel Orientation="Horizontal" Margin="0,16,0,0">
                        <TextBlock
                            Margin="25,10,0,10"
                            FontSize="14"
                            FontWeight="SemiBold"
                            Text="Adjacency Threshold:" />
                        <TextBox
                            Width="30"
                            Height="25"
                            Margin="10,0,0,0"
                            VerticalAlignment="Center"
                            PreviewTextInput="TextBox_PreviewTextInput"
                            Text="{Binding FilterSettings.AdjacencyThreshold, UpdateSourceTrigger=PropertyChanged, StringFormat=F0, ValidatesOnDataErrors=True}"
                            ToolTip="Set the adjacency threshold for fire stopping elements (in millimeters)." />
                        <TextBlock
                            Margin="0,10,0,10"
                            FontSize="14"
                            FontWeight="Regular"
                            Text="mm" />
                    </StackPanel>

                    <ScrollViewer
                        Grid.Row="1"
                        Margin="0,15,10,0"
                        VerticalScrollBarVisibility="Auto">
                        <StackPanel Visibility="{Binding IsSidebarCollapsed, Converter={StaticResource InverseBooleanToVisibilityConverter}}">

                            <!--  Linked Models Section  -->
                            <Expander Margin="0,0,0,16" IsExpanded="False">
                                <Expander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon
                                            Width="20"
                                            Height="20"
                                            Margin="0,0,8,0"
                                            VerticalAlignment="Center"
                                            Kind="Link" />
                                        <TextBlock
                                            FontWeight="Medium"
                                            Text="Linked Models"
                                            Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    </StackPanel>
                                </Expander.Header>
                                <StackPanel Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <StackPanel Margin="0,8,0,8" Orientation="Horizontal">
                                        <Button
                                            Height="24"
                                            Margin="0,0,8,0"
                                            Padding="8,0"
                                            BorderBrush="#12A8B2"
                                            Command="{Binding SelectAllLinkedModelsCommand}"
                                            Content="All"
                                            Foreground="#12A8B2"
                                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                                        <Button
                                            Height="24"
                                            Padding="8,0"
                                            BorderBrush="#12A8B2"
                                            Command="{Binding DeselectAllLinkedModelsCommand}"
                                            Content="None"
                                            Foreground="#12A8B2"
                                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                                    </StackPanel>
                                    <ItemsControl ItemsSource="{Binding AvailableLinkedModels}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <CheckBox
                                                    Margin="0,2"
                                                    Background="#12A8B2"
                                                    BorderBrush="#12A8B2"
                                                    Content="{Binding Name}"
                                                    Foreground="#12A8B2"
                                                    IsChecked="{Binding IsSelected}"
                                                    ToolTip="{Binding FilePath}" />
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </Expander>

                            <!--  Levels Section  -->
                            <Expander Margin="0,0,0,16" IsExpanded="False">
                                <Expander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon
                                            Width="20"
                                            Height="20"
                                            Margin="0,0,8,0"
                                            VerticalAlignment="Center"
                                            Kind="Building" />
                                        <TextBlock
                                            FontWeight="Medium"
                                            Text="Levels"
                                            Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    </StackPanel>
                                </Expander.Header>
                                <StackPanel Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <StackPanel Margin="0,8,0,8" Orientation="Horizontal">
                                        <Button
                                            Height="24"
                                            Margin="0,0,8,0"
                                            Padding="8,0"
                                            BorderBrush="#12A8B2"
                                            Command="{Binding SelectAllLevelsCommand}"
                                            Content="All"
                                            Foreground="#12A8B2"
                                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                                        <Button
                                            Height="24"
                                            Padding="8,0"
                                            BorderBrush="#12A8B2"
                                            Command="{Binding DeselectAllLevelsCommand}"
                                            Content="None"
                                            Foreground="#12A8B2"
                                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                                    </StackPanel>
                                    <ItemsControl ItemsSource="{Binding AvailableLevels}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <CheckBox
                                                    Margin="0,2"
                                                    Background="#12A8B2"
                                                    BorderBrush="#12A8B2"
                                                    Content="{Binding Name}"
                                                    Foreground="#12A8B2"
                                                    IsChecked="{Binding IsSelected}" />
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </Expander>

                            <!--  Categories Section  -->
                            <Expander Margin="0,0,0,16" IsExpanded="False">
                                <Expander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon
                                            Width="20"
                                            Height="20"
                                            Margin="0,0,8,0"
                                            VerticalAlignment="Center"
                                            Kind="Tag" />
                                        <TextBlock
                                            FontWeight="Medium"
                                            Text="Categories"
                                            Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    </StackPanel>
                                </Expander.Header>
                                <StackPanel Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <StackPanel Margin="0,8,0,8" Orientation="Horizontal">
                                        <Button
                                            Height="24"
                                            Margin="0,0,8,0"
                                            Padding="8,0"
                                            BorderBrush="#12A8B2"
                                            Command="{Binding SelectAllCategoriesCommand}"
                                            Content="All"
                                            Foreground="#12A8B2"
                                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                                        <Button
                                            Height="24"
                                            Padding="8,0"
                                            BorderBrush="#12A8B2"
                                            Command="{Binding DeselectAllCategoriesCommand}"
                                            Content="None"
                                            Foreground="#12A8B2"
                                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                                    </StackPanel>
                                    <ItemsControl ItemsSource="{Binding AvailableCategories}">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <CheckBox
                                                    Margin="0,2"
                                                    Background="#12A8B2"
                                                    BorderBrush="#12A8B2"
                                                    Content="{Binding Name}"
                                                    Foreground="#12A8B2"
                                                    IsChecked="{Binding IsSelected}"
                                                    ToolTip="{Binding Description}" />
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </StackPanel>
                            </Expander>

                            <!--  Filter Options  -->
                            <Expander Margin="0,0,0,16" IsExpanded="False">
                                <Expander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon
                                            Width="20"
                                            Height="20"
                                            Margin="0,0,8,0"
                                            VerticalAlignment="Center"
                                            Kind="Filter" />
                                        <TextBlock
                                            FontWeight="Medium"
                                            Text="Filter Options"
                                            Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    </StackPanel>
                                </Expander.Header>
                                <StackPanel Orientation="Horizontal" Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <CheckBox
                                        Margin="0,8,0,4"
                                        Content="Show Failures Only"
                                        IsChecked="{Binding FilterSettings.ShowFailuresOnly}" />
                                    <Button
                                        Height="20"
                                        Margin="100,5,0,0"
                                        Padding="10,0,10,0"
                                        Background="#12A8B2"
                                        Command="{Binding ApplyFiltersCommand}"
                                        Content="Show"
                                        FontSize="11"
                                        Foreground="White"
                                        IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                        Style="{StaticResource MaterialDesignRaisedButton}" />
                                </StackPanel>
                            </Expander>

                            <!--  Search  -->
                            <Expander Margin="0,0,0,16" IsExpanded="False">
                                <Expander.Header>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon
                                            Width="20"
                                            Height="20"
                                            Margin="0,0,8,0"
                                            VerticalAlignment="Center"
                                            Kind="Magnify" />
                                        <TextBlock
                                            FontWeight="Medium"
                                            Text="Search"
                                            Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}" />
                                    </StackPanel>
                                </Expander.Header>
                                <StackPanel Orientation="Horizontal" Visibility="{Binding IsSidebarExpanded, Converter={StaticResource BooleanToVisibilityConverter}}">
                                    <TextBox
                                        Width="240"
                                        Margin="0,8,10,4"
                                        materialDesign:HintAssist.Hint="Search elements..."
                                        Text="{Binding FilterSettings.SearchText, UpdateSourceTrigger=PropertyChanged}" />
                                    <Button 
                                        Background="Transparent" BorderBrush="Transparent"
                                        Command="{Binding ApplyFiltersCommand}"
                                        Padding="0,0,0,0"
                                        Foreground="White"
                                        IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                        Style="{StaticResource MaterialDesignRaisedButton}">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon
                                                Foreground="Black"
                                                Kind="Search" />
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Expander>

                            <!--  Action Buttons  -->
                            <!--<StackPanel Margin="0,16,0,0">

                                <Button
                                    Margin="10,5,10,5"
                                    Background="#12A8B2"
                                    Command="{Binding ApplyFiltersCommand}"
                                    Foreground="White"
                                    IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                    Style="{StaticResource MaterialDesignRaisedButton}">
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon
                                            Margin="0,0,8,0"
                                            Foreground="White"
                                            Kind="Update" />
                                        <TextBlock Foreground="White" Text="Update Filters" />
                                    </StackPanel>
                                </Button>
                                <Button
                                    Margin="10,3,10,0"
                                    Command="{Binding ClearFiltersCommand}"
                                    Content="Clear Filters"
                                    IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                    Style="{StaticResource MaterialDesignOutlinedButton}" />
                                <Button
                                    Margin="10,6,10,0"
                                    Command="{Binding RefreshFiltersCommand}"
                                    Content="Refresh"
                                    IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                                    Style="{StaticResource MaterialDesignFlatButton}" />
                            </StackPanel>-->
                        </StackPanel>
                    </ScrollViewer>

                    <Button
                        Grid.Row="2"
                        Margin="10,0,20,20"
                        Background="#12A8B2"
                        BorderBrush="#12A8B2"
                        Command="{Binding ExtractAndAnalyzeCommand}"
                        IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                        Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Margin="0,0,8,0"
                                Foreground="White"
                                Kind="Play" />
                            <TextBlock Foreground="White" Text="Extract &amp; Analyze" />
                        </StackPanel>
                    </Button>
                </Grid>
            </Expander>

            <!--  Main Data Area  -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  Summary Cards  -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <!--  Total Elements Card  -->
                    <materialDesign:Card
                        Grid.Column="0"
                        Margin="0,4,4,4"
                        Padding="16">
                        <StackPanel>
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource MaterialDesignBodyLight}"
                                Text="Total Elements" />
                            <TextBlock
                                FontSize="24"
                                FontWeight="Bold"
                                Text="{Binding TotalElementsCount}" />
                        </StackPanel>
                    </materialDesign:Card>

                    <!--  Failures Card  -->
                    <materialDesign:Card
                        Grid.Column="1"
                        Margin="4"
                        Padding="16">
                        <StackPanel>
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource MaterialDesignBodyLight}"
                                Text="Elements with Failures" />
                            <TextBlock
                                FontSize="24"
                                FontWeight="Bold"
                                Foreground="{DynamicResource MaterialDesignValidationErrorBrush}"
                                Text="{Binding FailuresCount}" />
                        </StackPanel>
                    </materialDesign:Card>

                    <!--  Success Rate Card  -->
                    <materialDesign:Card
                        Grid.Column="2"
                        Margin="4"
                        Padding="16">
                        <StackPanel>
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource MaterialDesignBodyLight}"
                                Text="Success Rate" />
                            <TextBlock FontSize="24" FontWeight="Bold">
                                <TextBlock.Text>
                                    <MultiBinding Converter="{StaticResource SuccessRateConverter}" StringFormat="{}{0:F1}%">
                                        <Binding Path="TotalElementsCount" />
                                        <Binding Path="FailuresCount" />
                                    </MultiBinding>
                                </TextBlock.Text>
                            </TextBlock>
                        </StackPanel>
                    </materialDesign:Card>

                    <!--  Filter Status Card  -->
                    <materialDesign:Card
                        Grid.Column="3"
                        Margin="4,4,10,4"
                        Padding="16">
                        <StackPanel>
                            <TextBlock
                                FontSize="12"
                                Foreground="{DynamicResource MaterialDesignBodyLight}"
                                Text="Active Filters" />
                            <TextBlock
                                Margin="0,10,0,0"
                                FontSize="12"
                                Text="{Binding FilterSettings.FilterSummary}"
                                TextWrapping="Wrap" />
                        </StackPanel>
                    </materialDesign:Card>
                </Grid>

                <!--  Data Grid  -->
                <materialDesign:Card Grid.Row="1" Margin="0,0,10,8">
                    <DataGrid
                        Margin="8"
                        materialDesign:DataGridAssist.CellPadding="8"
                        materialDesign:DataGridAssist.ColumnHeaderPadding="8"
                        AutoGenerateColumns="False"
                        BorderBrush="LightGray"
                        BorderThickness="1"
                        CanUserAddRows="False"
                        CanUserDeleteRows="False"
                        FrozenColumnCount="2"
                        GridLinesVisibility="All"
                        IsReadOnly="True"
                        ItemsSource="{Binding FilteredFireStoppingElements}"
                        SelectedItem="{Binding SelectedElement}">
                        <DataGrid.Columns>

                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding ElementId}"
                                Header="Element ID">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="LightGray" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="LightGray" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <!--  Header Background  -->
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding LevelName}"
                                Header="Level">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="LightGray" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="LightGray" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>

                            <!--  Fire Stopping Info Group  -->
                            <DataGridTextColumn
                                Width="150"
                                Binding="{Binding FamilyName}"
                                Header="Family Name">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#80FFCE00" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#80FFCE00" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding BecaTypeMark}"
                                Header="Type Mark">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#80FFCE00" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#80FFCE00" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding BecaInstMark}"
                                Header="Inst Mark">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#80FFCE00" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#80FFCE00" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>

                            <!--  Service Info Group  -->
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding ConnectedService.ElementId}"
                                Header="Element ID">
                                <!--  Define CellStyle for cell appearance  -->
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#CC8D0E84" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="IsTabStop" Value="False" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <!--  Make text selectable within the cell using a TextBox  -->
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="NoWrap" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                                <DataGridTextColumn.EditingElementStyle>
                                    <Style TargetType="TextBox">
                                        <Setter Property="IsReadOnly" Value="True" />
                                        <Setter Property="Background" Value="#CC8D0E84" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
                                    </Style>
                                </DataGridTextColumn.EditingElementStyle>
                                <!--  Define Header Style  -->
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#CC8D0E84" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding ConnectedService.ServiceType}"
                                Header="Service Type">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#CC8D0E84" />
                                        <Setter Property="Foreground" Value="White" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#CC8D0E84" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding ConnectedService.Size}"
                                Header="Service Size">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#CC8D0E84" />
                                        <Setter Property="Foreground" Value="White" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#CC8D0E84" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>

                            <!--  Structure Info Group  -->
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding AdjacentStructure.ElementId}"
                                Header="Element ID">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#CC01379b" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="IsTabStop" Value="False" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <!--  Make text selectable within the cell using a TextBox  -->
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="TextWrapping" Value="NoWrap" />
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                                <DataGridTextColumn.EditingElementStyle>
                                    <Style TargetType="TextBox">
                                        <Setter Property="IsReadOnly" Value="True" />
                                        <Setter Property="Background" Value="#CC01379b" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderThickness" Value="0" />
                                        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
                                    </Style>
                                </DataGridTextColumn.EditingElementStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#CC01379b" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding AdjacentStructure.StructureType}"
                                Header="Structure Type">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#CC01379b" />
                                        <Setter Property="Foreground" Value="White" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#CC01379b" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                            <DataGridTextColumn
                                Width="100"
                                Binding="{Binding AdjacentStructure.FireRating}"
                                Header="Fire Rating">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="#CC01379b" />
                                        <Setter Property="Foreground" Value="White" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#CC01379b" />
                                        <Setter Property="Foreground" Value="White" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>

                            <!--  Design Check Results Group  -->
                            <DataGridTemplateColumn Width="120" Header="Not Touching Wall">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Foreground="{Binding DesignCheckResult.NotTouchingWall, Converter={StaticResource BooleanToColorConverter}}" Text="{Binding DesignCheckResult.NotTouchingWall, Converter={StaticResource BooleanToPassFailConverter}}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                        <Setter Property="BorderBrush" Value="LightGray" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                    </Style>
                                </DataGridTemplateColumn.CellStyle>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Width="130" Header="Not Touching Service">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Foreground="{Binding DesignCheckResult.NotTouchingService, Converter={StaticResource BooleanToColorConverter}}" Text="{Binding DesignCheckResult.NotTouchingService, Converter={StaticResource BooleanToPassFailConverter}}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                        <Setter Property="BorderBrush" Value="LightGray" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                    </Style>
                                </DataGridTemplateColumn.CellStyle>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Width="80" Header="Clashing">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Foreground="{Binding DesignCheckResult.Clashing, Converter={StaticResource BooleanToColorConverter}}" Text="{Binding DesignCheckResult.Clashing, Converter={StaticResource BooleanToPassFailConverter}}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                        <Setter Property="BorderBrush" Value="LightGray" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                    </Style>
                                </DataGridTemplateColumn.CellStyle>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Width="80" Header="Adjacent">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Foreground="{Binding DesignCheckResult.Adjacent, Converter={StaticResource BooleanToColorConverter}}" Text="{Binding DesignCheckResult.Adjacent, Converter={StaticResource BooleanToPassFailConverter}}" />
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                        <Setter Property="BorderBrush" Value="LightGray" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="Background" Value="WhiteSmoke" />
                                    </Style>
                                </DataGridTemplateColumn.CellStyle>
                            </DataGridTemplateColumn>

                            <!--  Failure Summary  -->
                            <DataGridTextColumn
                                Width="200"
                                Binding="{Binding DesignCheckResult.FailureSummary}"
                                Header="Failure Summary">
                                <DataGridTextColumn.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding DesignCheckResult.FailureSummary}" Value="No Failures">
                                                <Setter Property="Foreground" Value="Green" />
                                                <Setter Property="Background" Value="LightGray" />
                                            </DataTrigger>
                                        </Style.Triggers>
                                        <Setter Property="Background" Value="LightGray" />
                                        <Setter Property="Foreground" Value="Red" />
                                    </Style>
                                </DataGridTextColumn.CellStyle>
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="LightGray" />
                                        <Setter Property="BorderBrush" Value="WhiteSmoke" />
                                        <Setter Property="BorderThickness" Value="1" />
                                        <Setter Property="Height" Value="40" />
                                        <Setter Property="HorizontalContentAlignment" Value="Center" />
                                        <Setter Property="FontWeight" Value="SemiBold" />
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </materialDesign:Card>
            </Grid>
        </Grid>

        <!--  Action  -->
        <materialDesign:ColorZone
            Grid.Row="3"
            Margin="10,0,10,0"
            Padding="16,8"
            Mode="Light">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <!--  Title  -->
                <TextBlock
                    Grid.Column="1"
                    Margin="16,0"
                    VerticalAlignment="Center"
                    FontSize="18"
                    FontWeight="Medium"
                    Text="" />

                <!--  Action Buttons  -->
                <StackPanel
                    Grid.Column="2"
                    Margin="0,0,0,10"
                    Orientation="Horizontal">
                    <!--<Button
                        Margin="8,0"
                        Background="Red"
                        BorderBrush="Red"
                        Command="{Binding RunPerformanceTestCommand}"
                        IsEnabled="False"
                        Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Margin="0,0,8,0"
                                Foreground="White"
                                Kind="TestTube" />
                            <TextBlock Foreground="White" Text="Test Performance" />
                        </StackPanel>
                    </Button>-->
                    <Button
                        Margin="8,0"
                        Background="#FFFFCE00"
                        BorderBrush="#FFFFCE00"
                        Command="{Binding ExportToExcelCommand}"
                        IsEnabled="{Binding IsOperationInProgress, Converter={StaticResource InverseBooleanConverter}}"
                        Style="{StaticResource MaterialDesignRaisedButton}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon
                                Margin="0,0,8,0"
                                Foreground="Black"
                                Kind="FileExcel" />
                            <TextBlock Foreground="Black" Text="Export to Excel" />
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!--  Status Bar  -->
        <materialDesign:ColorZone
            Grid.Row="2"
            Margin="10,0,10,0"
            Padding="16,8"
            Mode="Light">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>

                <TextBlock
                    Grid.Column="0"
                    VerticalAlignment="Center"
                    Text="{Binding StatusMessage}" />

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <ProgressBar
                        Width="360"
                        Height="6"
                        Margin="16,0"
                        Background="White"
                        BorderBrush="#FF8D0E84"
                        Foreground="#FF8D0E84"
                        Maximum="100"
                        Visibility="{Binding IsOperationInProgress, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Value="{Binding ProgressPercentage}" />

                    <TextBlock
                        Margin="8,0"
                        VerticalAlignment="Center"
                        Text="{Binding ProgressPercentage, StringFormat={}{0:F0}%}"
                        Visibility="{Binding IsOperationInProgress, Converter={StaticResource BooleanToVisibilityConverter}}" />

                    <Button
                        Command="{Binding CancelOperationCommand}"
                        Style="{StaticResource MaterialDesignIconButton}"
                        ToolTip="Cancel Operation"
                        Visibility="{Binding IsOperationInProgress, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <materialDesign:PackIcon
                            Width="20"
                            Height="20"
                            Foreground="Red"
                            Kind="Cancel" />
                    </Button>
                </StackPanel>
            </Grid>
        </materialDesign:ColorZone>

        <!--  Footer  -->
        <TextBlock
            Grid.Row="4"
            Grid.Column="5"
            Margin="0,5,15,10"
            HorizontalAlignment="Right"
            Style="{StaticResource MaterialDesignHeadline6TextBlock}"
            Text="Make Everyday Better" />
        <Image
            Grid.Row="4"
            Height="25"
            Margin="15,5,0,10"
            HorizontalAlignment="Left"
            Source="/MEP.Pacifire;component/Resources/BecaLogoBlack.png" />
    </Grid>
</Window>
