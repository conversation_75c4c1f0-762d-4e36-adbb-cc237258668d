using MEP.Pacifire.Helpers;
using System;
using System.Collections.Concurrent;
using System.IO;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace MEP.Pacifire.Diagnostics
{
    /// <summary>
    /// High-performance logging system for the Fire Stopping Solution Exporter.
    /// Provides structured logging with different levels and async file writing.
    /// </summary>
    public class Logger : IDisposable
    {
        private static readonly Lazy<Logger> _instance = new(() => new Logger());
        public static Logger Instance => _instance.Value;

        private readonly ConcurrentQueue<LogEntry> _logQueue = new();
        private readonly CancellationTokenSource _cancellationTokenSource = new();
        private readonly Task _writerTask;
        private readonly string _logDirectory;
        private readonly string _logFileName;
        private bool _disposed = false;

        /// <summary>
        /// Current log level filter
        /// </summary>
        public LogLevel MinimumLogLevel { get; set; } = LogLevel.Information;

        /// <summary>
        /// Whether to write logs to file
        /// </summary>
        public bool WriteToFile { get; set; } = true;

        /// <summary>
        /// Whether to write logs to debug output
        /// </summary>
        public bool WriteToDebug { get; set; } = true;

        /// <summary>
        /// Maximum log file size in MB before rotation
        /// </summary>
        public int MaxLogFileSizeMB { get; set; } = 10;

        /// <summary>
        /// Number of log files to keep during rotation
        /// </summary>
        public int MaxLogFiles { get; set; } = 5;

        private Logger()
        {
            _logDirectory = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "MEP.Pacifire", "Logs");

            _logFileName = $"Pacifire_{DateTime.Now:yyyyMMdd}.log";

            // Ensure log directory exists
            Directory.CreateDirectory(_logDirectory);

            // Start background writer task
            _writerTask = Task.Run(ProcessLogQueue, _cancellationTokenSource.Token);
        }

        #region Public Logging Methods

        /// <summary>
        /// Logs a trace message
        /// </summary>
        public void Trace(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Trace, message, null, memberName, filePath, lineNumber);
        }

        /// <summary>
        /// Logs a debug message
        /// </summary>
        public void Debug(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Debug, message, null, memberName, filePath, lineNumber);
        }

        /// <summary>
        /// Logs an information message
        /// </summary>
        public void Information(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Information, message, null, memberName, filePath, lineNumber);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        public void Warning(string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Warning, message, null, memberName, filePath, lineNumber);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        public void Error(string message, Exception? exception = null, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Error, message, exception, memberName, filePath, lineNumber);
        }

        /// <summary>
        /// Logs a critical error message
        /// </summary>
        public void Critical(string message, Exception? exception = null, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Log(LogLevel.Critical, message, exception, memberName, filePath, lineNumber);
        }

        /// <summary>
        /// Logs an operation start
        /// </summary>
        public IDisposable BeginOperation(string operationName, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNumber = 0)
        {
            Information($"Starting operation: {operationName}", memberName, filePath, lineNumber);
            return new OperationScope(this, operationName, memberName, filePath, lineNumber);
        }

        #endregion

        #region Private Methods

        private void Log(LogLevel level, string message, Exception? exception, string memberName, string filePath, int lineNumber)
        {
            if (level < MinimumLogLevel) return;

            var entry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                Exception = exception,
                MemberName = memberName,
                FileName = Path.GetFileName(filePath),
                LineNumber = lineNumber,
                ThreadId = Thread.CurrentThread.ManagedThreadId
            };

            _logQueue.Enqueue(entry);

            // Also write to debug output immediately for debugging
            if (WriteToDebug)
            {
                System.Diagnostics.Debug.WriteLine(FormatLogEntry(entry));
            }
        }

        private async Task ProcessLogQueue()
        {
            var buffer = new StringBuilder();
            
            while (!_cancellationTokenSource.Token.IsCancellationRequested)
            {
                try
                {
                    // Process all queued entries
                    while (_logQueue.TryDequeue(out var entry))
                    {
                        buffer.AppendLine(FormatLogEntry(entry));
                    }

                    // Write buffer to file if we have content
                    if (buffer.Length > 0 && WriteToFile)
                    {
                        await WriteToFileAsync(buffer.ToString());
                        buffer.Clear();
                    }

                    // Wait a bit before checking again
                    await Task.Delay(100, _cancellationTokenSource.Token);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in log writer: {ex.Message}");
                }
            }

            // Process any remaining entries
            while (_logQueue.TryDequeue(out var entry))
            {
                buffer.AppendLine(FormatLogEntry(entry));
            }

            if (buffer.Length > 0 && WriteToFile)
            {
                await WriteToFileAsync(buffer.ToString());
            }
        }

        private async Task WriteToFileAsync(string content)
        {
            try
            {
                var filePath = Path.Combine(_logDirectory, _logFileName);
                
                // Check if we need to rotate the log file
                if (File.Exists(filePath))
                {
                    var fileInfo = new FileInfo(filePath);
                    if (fileInfo.Length > MaxLogFileSizeMB * 1024 * 1024)
                    {
                        RotateLogFiles();
                    }
                }

                await FileHelper.AppendAllTextAsync(filePath, content);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error writing to log file: {ex.Message}");
            }
        }

        private void RotateLogFiles()
        {
            try
            {
                var baseFileName = Path.GetFileNameWithoutExtension(_logFileName);
                var extension = Path.GetExtension(_logFileName);

                // Move existing numbered files up
                for (int i = MaxLogFiles - 1; i >= 1; i--)
                {
                    var oldFile = Path.Combine(_logDirectory, $"{baseFileName}.{i}{extension}");
                    var newFile = Path.Combine(_logDirectory, $"{baseFileName}.{i + 1}{extension}");
                    
                    if (File.Exists(oldFile))
                    {
                        if (File.Exists(newFile))
                            File.Delete(newFile);
                        File.Move(oldFile, newFile);
                    }
                }

                // Move current file to .1
                var currentFile = Path.Combine(_logDirectory, _logFileName);
                var firstBackup = Path.Combine(_logDirectory, $"{baseFileName}.1{extension}");
                
                if (File.Exists(currentFile))
                {
                    if (File.Exists(firstBackup))
                        File.Delete(firstBackup);
                    File.Move(currentFile, firstBackup);
                }

                // Delete oldest file if it exists
                var oldestFile = Path.Combine(_logDirectory, $"{baseFileName}.{MaxLogFiles + 1}{extension}");
                if (File.Exists(oldestFile))
                {
                    File.Delete(oldestFile);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error rotating log files: {ex.Message}");
            }
        }

        private static string FormatLogEntry(LogEntry entry)
        {
            var sb = new StringBuilder();
            
            sb.Append($"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff} ");
            sb.Append($"[{entry.Level.ToString().ToUpper().PadRight(5)}] ");
            sb.Append($"[T{entry.ThreadId:D2}] ");
            sb.Append($"{entry.FileName}:{entry.LineNumber} ");
            sb.Append($"{entry.MemberName}() - ");
            sb.Append(entry.Message);

            if (entry.Exception != null)
            {
                sb.AppendLine();
                sb.Append($"Exception: {entry.Exception}");
            }

            return sb.ToString();
        }

        #endregion

        #region Diagnostics Methods

        /// <summary>
        /// Gets current log statistics
        /// </summary>
        public LogStatistics GetStatistics()
        {
            var logFilePath = Path.Combine(_logDirectory, _logFileName);
            var fileSize = File.Exists(logFilePath) ? new FileInfo(logFilePath).Length : 0;

            return new LogStatistics
            {
                QueuedEntries = _logQueue.Count,
                LogFilePath = logFilePath,
                LogFileSizeBytes = fileSize,
                MinimumLogLevel = MinimumLogLevel,
                WriteToFile = WriteToFile,
                WriteToDebug = WriteToDebug
            };
        }

        /// <summary>
        /// Flushes all pending log entries
        /// </summary>
        public async Task FlushAsync()
        {
            // Wait for queue to be processed
            var timeout = DateTime.Now.AddSeconds(5);
            while (_logQueue.Count > 0 && DateTime.Now < timeout)
            {
                await Task.Delay(50);
            }
        }

        /// <summary>
        /// Gets recent log entries from file
        /// </summary>
        public async Task<string[]> GetRecentLogEntriesAsync(int maxLines = 100)
        {
            try
            {
                var logFilePath = Path.Combine(_logDirectory, _logFileName);
                if (!File.Exists(logFilePath))
                    return Array.Empty<string>();

                var lines = await FileHelper.ReadAllLinesAsync(logFilePath);
                var startIndex = Math.Max(0, lines.Length - maxLines);
                var result = new string[lines.Length - startIndex];
                Array.Copy(lines, startIndex, result, 0, result.Length);
                return result;
            }
            catch (Exception ex)
            {
                Error($"Error reading recent log entries: {ex.Message}", ex);
                return Array.Empty<string>();
            }
        }

        #endregion

        #region IDisposable

        public void Dispose()
        {
            if (!_disposed)
            {
                _cancellationTokenSource.Cancel();
                
                try
                {
                    _writerTask.Wait(TimeSpan.FromSeconds(5));
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error waiting for log writer task: {ex.Message}");
                }

                _cancellationTokenSource.Dispose();
                _disposed = true;
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Log levels in order of severity
    /// </summary>
    public enum LogLevel
    {
        Trace = 0,
        Debug = 1,
        Information = 2,
        Warning = 3,
        Error = 4,
        Critical = 5
    }

    /// <summary>
    /// Represents a single log entry
    /// </summary>
    internal class LogEntry
    {
        public DateTime Timestamp { get; set; }
        public LogLevel Level { get; set; }
        public string Message { get; set; } = string.Empty;
        public Exception? Exception { get; set; }
        public string MemberName { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public int LineNumber { get; set; }
        public int ThreadId { get; set; }
    }

    /// <summary>
    /// Operation scope for tracking operation duration
    /// </summary>
    internal class OperationScope : IDisposable
    {
        private readonly Logger _logger;
        private readonly string _operationName;
        private readonly string _memberName;
        private readonly string _filePath;
        private readonly int _lineNumber;
        private readonly DateTime _startTime;

        public OperationScope(Logger logger, string operationName, string memberName, string filePath, int lineNumber)
        {
            _logger = logger;
            _operationName = operationName;
            _memberName = memberName;
            _filePath = filePath;
            _lineNumber = lineNumber;
            _startTime = DateTime.Now;
        }

        public void Dispose()
        {
            var duration = DateTime.Now - _startTime;
            _logger.Information($"Completed operation: {_operationName} (Duration: {duration.TotalMilliseconds:F0}ms)", _memberName, _filePath, _lineNumber);
        }
    }

    /// <summary>
    /// Log statistics for diagnostics
    /// </summary>
    public class LogStatistics
    {
        public int QueuedEntries { get; set; }
        public string LogFilePath { get; set; } = string.Empty;
        public long LogFileSizeBytes { get; set; }
        public LogLevel MinimumLogLevel { get; set; }
        public bool WriteToFile { get; set; }
        public bool WriteToDebug { get; set; }

        public string LogFileSizeFormatted
        {
            get
            {
                if (LogFileSizeBytes < 1024) return $"{LogFileSizeBytes} B";
                if (LogFileSizeBytes < 1024 * 1024) return $"{LogFileSizeBytes / 1024.0:F1} KB";
                return $"{LogFileSizeBytes / (1024.0 * 1024.0):F1} MB";
            }
        }
    }

    #endregion
}
