﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.UI;
using Autodesk.Revit.DB;
using BecaCommand;
using MEP.Pacifire.Services;
using MEP.Pacifire.ViewModels;
using MEP.Pacifire.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using MessageBox = System.Windows.MessageBox;

namespace MEP.Pacifire.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    internal class PacifireCommand : BecaBaseCommand
    {
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            try
            {
                UIApplication uiapp = commandData.Application;
                UIDocument uidoc = uiapp.ActiveUIDocument;
                var app = uiapp.Application;
                Document doc = uidoc.Document;

                // Configure dependency injection
                var serviceProvider = ServiceContainer.ConfigureServices(doc);

                // Validate service registration
                if (!ServiceContainer.ValidateServices())
                {
                    message = "Failed to configure services. Please check the installation.";
                    return Result.Failed;
                }

                // Get the main ViewModel from DI container
                var mainViewModel = ServiceContainer.GetRequiredService<MainViewModel>();
                var mainView = new MainView(mainViewModel);

                // Show the main window
                var result = mainView.ShowDialog();

                // Clean up
                ServiceContainer.Dispose();

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                message = $"An error occurred while launching the Fire Stopping Exporter: {ex.Message}";

                // Log detailed error for debugging
                System.Diagnostics.Debug.WriteLine($"PacifireCommand Error: {ex}");

                // Show error to user
                MessageBox.Show(
                    $"An error occurred while launching the application:\n\n{ex.Message}\n\nPlease contact support if this issue persists.",
                    "Fire Stopping Exporter Error",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);

                return Result.Failed;
            }
        }

        public override string GetAddinAuthor()
        {
            return "Tristan Balme, Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.Pacifire.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
