using System;
using System.Diagnostics;
using Autodesk.Revit.DB;
using MEP.Pacifire.Services;

namespace MEP.Pacifire.Tests
{
    /// <summary>
    /// Test class to verify coordinate transformation fixes for Internal Origin issues
    /// </summary>
    public static class CoordinateTransformTest
    {
        /// <summary>
        /// Test method to verify GetTotalTransform vs GetTransform behavior
        /// Call this from a Revit command to test with actual linked models
        /// </summary>
        public static void TestCoordinateTransforms(Document hostDocument)
        {
            try
            {
                Debug.WriteLine("=== COORDINATE TRANSFORM TEST ===");
                Debug.WriteLine($"Host Document: {hostDocument.Title}");
                
                // Find all RevitLinkInstances in the host document
                var linkInstances = new FilteredElementCollector(hostDocument)
                    .OfClass(typeof(RevitLinkInstance))
                    .Cast<RevitLinkInstance>()
                    .Where(link => link.GetLinkDocument() != null)
                    .ToList();

                if (!linkInstances.Any())
                {
                    Debug.WriteLine("No linked models found in the host document.");
                    return;
                }

                Debug.WriteLine($"Found {linkInstances.Count} linked models:");

                foreach (var linkInstance in linkInstances)
                {
                    var linkedDoc = linkInstance.GetLinkDocument();
                    Debug.WriteLine($"\n--- Testing Link: {linkInstance.Name} ---");
                    Debug.WriteLine($"Linked Document: {linkedDoc?.Title ?? "NULL"}");

                    // Compare GetTransform() vs GetTotalTransform()
                    var standardTransform = linkInstance.GetTransform();
                    var totalTransform = linkInstance.GetTotalTransform();

                    Debug.WriteLine($"Standard Transform Origin: ({standardTransform.Origin.X:F2}, {standardTransform.Origin.Y:F2}, {standardTransform.Origin.Z:F2})");
                    Debug.WriteLine($"Total Transform Origin:    ({totalTransform.Origin.X:F2}, {totalTransform.Origin.Y:F2}, {totalTransform.Origin.Z:F2})");

                    // Calculate the difference
                    var originDifference = totalTransform.Origin - standardTransform.Origin;
                    var differenceDistance = originDifference.GetLength();

                    Debug.WriteLine($"Origin Difference: ({originDifference.X:F2}, {originDifference.Y:F2}, {originDifference.Z:F2})");
                    Debug.WriteLine($"Difference Distance: {differenceDistance:F2} feet ({differenceDistance * 304.8:F2} mm)");

                    if (differenceDistance > 0.01) // More than ~3mm difference
                    {
                        Debug.WriteLine("⚠️  SIGNIFICANT DIFFERENCE DETECTED - This indicates Internal Origin offset!");
                    }
                    else
                    {
                        Debug.WriteLine("✅ Transforms are nearly identical - No Internal Origin issues");
                    }

                    // Test with a sample point transformation
                    var testPoint = new XYZ(0, 0, 0); // Origin point in linked model
                    var transformedStandard = standardTransform.OfPoint(testPoint);
                    var transformedTotal = totalTransform.OfPoint(testPoint);

                    Debug.WriteLine($"Test Point (0,0,0) Transformations:");
                    Debug.WriteLine($"  Standard: ({transformedStandard.X:F2}, {transformedStandard.Y:F2}, {transformedStandard.Z:F2})");
                    Debug.WriteLine($"  Total:    ({transformedTotal.X:F2}, {transformedTotal.Y:F2}, {transformedTotal.Z:F2})");
                }

                Debug.WriteLine("\n=== TEST COMPLETE ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error during coordinate transform test: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Test intersection detection with the new coordinate system
        /// </summary>
        public static void TestIntersectionDetection(Document hostDocument)
        {
            try
            {
                Debug.WriteLine("=== INTERSECTION DETECTION TEST ===");
                
                // This would need to be called from within the application context
                // where ExtractionService and DesignCheckService are available
                Debug.WriteLine("This test should be run from within the main application context.");
                Debug.WriteLine("It will verify that Boolean intersections now work correctly with GetTotalTransform().");
                
                Debug.WriteLine("=== TEST PLACEHOLDER COMPLETE ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error during intersection test: {ex.Message}");
            }
        }
    }
}
