using System;
using System.Linq;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Helper class for geometry operations and coordinate transformations.
    /// Critical for accurate spatial analysis across linked models.
    /// </summary>
    public class GeometryHelper : IGeometryHelper
    {
        private const double TOLERANCE = 1e-6;
        private const double FEET_TO_MM = 304.8;
        private const double MM_TO_FEET = 1.0 / 304.8;

        /// <summary>
        /// Transforms element geometry from linked model coordinates to host model coordinates
        /// </summary>
        public void TransformElementGeometry(Element element, Transform linkTransform, FireStoppingElement targetElement)
        {
            try
            {
                // Get and transform location
                var location = GetElementLocation(element);
                targetElement.LocationPoint = TransformPoint(location, linkTransform);

                // Get and transform bounding box
                var boundingBox = GetElementBoundingBox(element);
                if (boundingBox != null)
                {
                    targetElement.BoundingBox = TransformBoundingBox(boundingBox, linkTransform);
                }

                // Get and transform solid geometry
                var solid = ExtractSolid(element);
                if (solid != null)
                {
                    targetElement.TransformedSolid = TransformSolid(solid, linkTransform);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error transforming fire stopping geometry: {ex.Message}");
                // Set fallback values
                targetElement.LocationPoint = XYZ.Zero;
                targetElement.BoundingBox = null;
                targetElement.TransformedSolid = null;
            }
        }

        /// <summary>
        /// Transforms element geometry from linked model coordinates to host model coordinates
        /// </summary>
        public void TransformElementGeometry(Element element, Transform linkTransform, ServiceElement targetElement)
        {
            try
            {
                var location = GetElementLocation(element);
                targetElement.LocationPoint = TransformPoint(location, linkTransform);

                var boundingBox = GetElementBoundingBox(element);
                if (boundingBox != null)
                {
                    targetElement.BoundingBox = TransformBoundingBox(boundingBox, linkTransform);
                }

                var solid = ExtractSolid(element);
                if (solid != null)
                {
                    targetElement.TransformedSolid = TransformSolid(solid, linkTransform);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error transforming service geometry: {ex.Message}");
                targetElement.LocationPoint = XYZ.Zero;
                targetElement.BoundingBox = null;
                targetElement.TransformedSolid = null;
            }
        }

        /// <summary>
        /// Transforms element geometry from linked model coordinates to host model coordinates
        /// </summary>
        public void TransformElementGeometry(Element element, Transform linkTransform, StructuralElement targetElement)
        {
            try
            {
                var location = GetElementLocation(element);
                targetElement.LocationPoint = TransformPoint(location, linkTransform);

                var boundingBox = GetElementBoundingBox(element);
                if (boundingBox != null)
                {
                    targetElement.BoundingBox = TransformBoundingBox(boundingBox, linkTransform);
                }

                var solid = ExtractSolid(element);
                if (solid != null)
                {
                    targetElement.TransformedSolid = TransformSolid(solid, linkTransform);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error transforming structural geometry: {ex.Message}");
                targetElement.LocationPoint = XYZ.Zero;
                targetElement.BoundingBox = null;
                targetElement.TransformedSolid = null;
            }
        }

        /// <summary>
        /// Extracts solid geometry from an element
        /// </summary>
        public Solid? ExtractSolid(Element element)
        {
            try
            {
                var geometryElement = element.get_Geometry(new Options());
                if (geometryElement == null) return null;

                foreach (GeometryObject geometryObject in geometryElement)
                {
                    // Handle solid directly
                    if (geometryObject is Solid solid && IsValidSolid(solid))
                    {
                        return solid;
                    }

                    // Handle geometry instance (for families)
                    if (geometryObject is GeometryInstance geometryInstance)
                    {
                        var instanceGeometry = geometryInstance.GetInstanceGeometry();
                        if (instanceGeometry != null)
                        {
                            foreach (GeometryObject instanceObject in instanceGeometry)
                            {
                                if (instanceObject is Solid instanceSolid && IsValidSolid(instanceSolid))
                                {
                                    return instanceSolid;
                                }
                            }
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting solid: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Transforms a solid using the specified transform
        /// </summary>
        public Solid? TransformSolid(Solid solid, Transform transform)
        {
            try
            {
                if (solid == null || !IsValidSolid(solid)) return null;
                return SolidUtils.CreateTransformed(solid, transform);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error transforming solid: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the location point of an element
        /// </summary>
        public XYZ GetElementLocation(Element element)
        {
            try
            {
                if (element.Location is LocationPoint locationPoint)
                {
                    return locationPoint.Point;
                }

                if (element.Location is LocationCurve locationCurve)
                {
                    var curve = locationCurve.Curve;
                    return curve.Evaluate(0.5, true); // Midpoint
                }

                // Fallback to bounding box center
                var boundingBox = GetElementBoundingBox(element);
                if (boundingBox != null)
                {
                    return GetBoundingBoxCenter(boundingBox);
                }

                return XYZ.Zero;
            }
            catch (Exception)
            {
                return XYZ.Zero;
            }
        }

        /// <summary>
        /// Transforms a point using the specified transform
        /// </summary>
        public XYZ TransformPoint(XYZ point, Transform transform)
        {
            try
            {
                return transform.OfPoint(point);
            }
            catch (Exception)
            {
                return point;
            }
        }

        /// <summary>
        /// Gets the bounding box of an element
        /// </summary>
        public BoundingBoxXYZ? GetElementBoundingBox(Element element)
        {
            try
            {
                return element.get_BoundingBox(null);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Transforms a bounding box using the specified transform
        /// </summary>
        public BoundingBoxXYZ? TransformBoundingBox(BoundingBoxXYZ boundingBox, Transform transform)
        {
            try
            {
                if (boundingBox == null) return null;

                var transformedBox = new BoundingBoxXYZ
                {
                    Min = transform.OfPoint(boundingBox.Min),
                    Max = transform.OfPoint(boundingBox.Max)
                };

                // Ensure Min is actually minimum and Max is maximum after transformation
                var actualMin = new XYZ(
                    Math.Min(transformedBox.Min.X, transformedBox.Max.X),
                    Math.Min(transformedBox.Min.Y, transformedBox.Max.Y),
                    Math.Min(transformedBox.Min.Z, transformedBox.Max.Z)
                );

                var actualMax = new XYZ(
                    Math.Max(transformedBox.Min.X, transformedBox.Max.X),
                    Math.Max(transformedBox.Min.Y, transformedBox.Max.Y),
                    Math.Max(transformedBox.Min.Z, transformedBox.Max.Z)
                );

                return new BoundingBoxXYZ { Min = actualMin, Max = actualMax };
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Checks if two solids intersect
        /// </summary>
        public bool DoSolidsIntersect(Solid solid1, Solid solid2)
        {
            try
            {
                if (!IsValidSolid(solid1) || !IsValidSolid(solid2)) return false;

                var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                    solid1, solid2, BooleanOperationsType.Intersect);

                return intersection != null && intersection.Volume > TOLERANCE;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// Calculates the intersection volume between two solids
        /// </summary>
        public double CalculateIntersectionVolume(Solid solid1, Solid solid2)
        {
            try
            {
                if (!IsValidSolid(solid1) || !IsValidSolid(solid2)) return 0.0;

                var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                    solid1, solid2, BooleanOperationsType.Intersect);

                return intersection?.Volume ?? 0.0;
            }
            catch (Exception)
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Checks if two bounding boxes intersect
        /// </summary>
        public bool DoBoundingBoxesIntersect(BoundingBoxXYZ box1, BoundingBoxXYZ box2)
        {
            if (box1 == null || box2 == null) return false;

            return !(box1.Max.X < box2.Min.X || box2.Max.X < box1.Min.X ||
                     box1.Max.Y < box2.Min.Y || box2.Max.Y < box1.Min.Y ||
                     box1.Max.Z < box2.Min.Z || box2.Max.Z < box1.Min.Z);
        }

        /// <summary>
        /// Calculates the distance between two points
        /// </summary>
        public double CalculateDistance(XYZ point1, XYZ point2)
        {
            if (point1 == null || point2 == null) return double.MaxValue;
            return point1.DistanceTo(point2);
        }

        /// <summary>
        /// Calculates the distance between two bounding boxes
        /// </summary>
        public double CalculateBoundingBoxDistance(BoundingBoxXYZ box1, BoundingBoxXYZ box2)
        {
            if (box1 == null || box2 == null) return double.MaxValue;

            if (DoBoundingBoxesIntersect(box1, box2)) return 0.0;

            var center1 = GetBoundingBoxCenter(box1);
            var center2 = GetBoundingBoxCenter(box2);

            return CalculateDistance(center1, center2);
        }

        /// <summary>
        /// Expands a bounding box by the specified distance
        /// </summary>
        public BoundingBoxXYZ ExpandBoundingBox(BoundingBoxXYZ boundingBox, double expansion)
        {
            if (boundingBox == null) return null;

            var expansionVector = new XYZ(expansion, expansion, expansion);
            return new BoundingBoxXYZ
            {
                Min = boundingBox.Min - expansionVector,
                Max = boundingBox.Max + expansionVector
            };
        }

        /// <summary>
        /// Converts distance from feet to millimeters
        /// </summary>
        public double FeetToMillimeters(double feet)
        {
            return feet * FEET_TO_MM;
        }

        /// <summary>
        /// Converts distance from millimeters to feet
        /// </summary>
        public double MillimetersToFeet(double millimeters)
        {
            return millimeters * MM_TO_FEET;
        }

        /// <summary>
        /// Gets the center point of a bounding box
        /// </summary>
        public XYZ GetBoundingBoxCenter(BoundingBoxXYZ boundingBox)
        {
            if (boundingBox == null) return XYZ.Zero;

            return new XYZ(
                (boundingBox.Min.X + boundingBox.Max.X) / 2,
                (boundingBox.Min.Y + boundingBox.Max.Y) / 2,
                (boundingBox.Min.Z + boundingBox.Max.Z) / 2
            );
        }

        /// <summary>
        /// Gets the center point of a UV bounding box on a face
        /// </summary>
        public XYZ GetBoundingBoxCenter(BoundingBoxUV boundingBox, Face face)
        {
            if (boundingBox == null || face == null) return XYZ.Zero;

            var centerU = (boundingBox.Min.U + boundingBox.Max.U) / 2;
            var centerV = (boundingBox.Min.V + boundingBox.Max.V) / 2;
            var centerUV = new UV(centerU, centerV);

            return face.Evaluate(centerUV);
        }

        /// <summary>
        /// Validates that a solid is suitable for intersection operations
        /// </summary>
        public bool IsValidSolid(Solid solid)
        {
            return solid != null && solid.Volume > TOLERANCE && solid.SurfaceArea > TOLERANCE;
        }

        /// <summary>
        /// Creates a simplified bounding box solid for performance testing
        /// </summary>
        public Solid? CreateBoundingBoxSolid(BoundingBoxXYZ boundingBox)
        {
            try
            {
                if (boundingBox == null) return null;

                var min = boundingBox.Min;
                var max = boundingBox.Max;

                // Create a simple box solid
                var profile = new List<Curve>
                {
                    Line.CreateBound(new XYZ(min.X, min.Y, min.Z), new XYZ(max.X, min.Y, min.Z)),
                    Line.CreateBound(new XYZ(max.X, min.Y, min.Z), new XYZ(max.X, max.Y, min.Z)),
                    Line.CreateBound(new XYZ(max.X, max.Y, min.Z), new XYZ(min.X, max.Y, min.Z)),
                    Line.CreateBound(new XYZ(min.X, max.Y, min.Z), new XYZ(min.X, min.Y, min.Z))
                };

                var curveLoop = CurveLoop.Create(profile);
                var height = max.Z - min.Z;

                return GeometryCreationUtilities.CreateExtrusionGeometry(
                    new[] { curveLoop }, XYZ.BasisZ, height);
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Finds the closest point on a solid to a given point
        /// </summary>
        public XYZ? FindClosestPointOnSolid(Solid solid, XYZ point)
        {
            try
            {
                if (!IsValidSolid(solid) || point == null) return null;

                XYZ? closestPoint = null;
                var minDistance = double.MaxValue;

                // Check faces
                foreach (Face face in solid.Faces)
                {
                    var result = face.Project(point);
                    if (result != null)
                    {
                        var distance = point.DistanceTo(result.XYZPoint);
                        if (distance < minDistance)
                        {
                            minDistance = distance;
                            closestPoint = result.XYZPoint;
                        }
                    }
                }

                return closestPoint;
            }
            catch (Exception)
            {
                return null;
            }
        }

        /// <summary>
        /// Calculates the minimum distance between two solids
        /// </summary>
        public double CalculateMinimumSolidDistance(Solid solid1, Solid solid2)
        {
            try
            {
                if (!IsValidSolid(solid1) || !IsValidSolid(solid2)) return double.MaxValue;

                // If they intersect, distance is 0
                if (DoSolidsIntersect(solid1, solid2)) return 0.0;

                // Find minimum distance between faces
                var minDistance = double.MaxValue;

                foreach (Face face1 in solid1.Faces)
                {
                    foreach (Face face2 in solid2.Faces)
                    {
                        // Sample points on each face and find minimum distance
                        var bbox1 = face1.GetBoundingBox();
                        var bbox2 = face2.GetBoundingBox();

                        var center1 = GetBoundingBoxCenter(bbox1, face1);
                        var center2 = GetBoundingBoxCenter(bbox2, face2);

                        var distance = CalculateDistance(center1, center2);
                        if (distance < minDistance)
                        {
                            minDistance = distance;
                        }
                    }
                }

                return minDistance;
            }
            catch (Exception)
            {
                return double.MaxValue;
            }
        }
    }
}
