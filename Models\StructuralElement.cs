using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;
using Autodesk.Revit.DB;

namespace MEP.Pacifire.Models
{
    /// <summary>
    /// Represents a structural element (Wall, Floor) from architectural/structural linked models.
    /// Contains parameters relevant for fire stopping analysis and fire rating validation.
    /// </summary>
    public partial class StructuralElement : ObservableObject
    {
        /// <summary>
        /// Unique identifier for the Revit element
        /// </summary>
        [ObservableProperty]
        private ElementId _elementId;

        /// <summary>
        /// Reference to the source Revit element
        /// </summary>
        [ObservableProperty]
        private Element _revitElement;

        /// <summary>
        /// Reference to the linked model instance containing this element
        /// </summary>
        [ObservableProperty]
        private RevitLinkInstance _linkInstance;

        /// <summary>
        /// Type of structure (Wall, Floor, Ceiling, etc.)
        /// </summary>
        [ObservableProperty]
        private StructureType _structureType;

        /// <summary>
        /// Material type of the structural element
        /// </summary>
        [ObservableProperty]
        private string _materialType = string.Empty;

        /// <summary>
        /// Fire rating of the structural element (e.g., "2HR", "1HR", "30MIN")
        /// </summary>
        [ObservableProperty]
        private string _fireRating = string.Empty;

        /// <summary>
        /// Thickness of the structural element in millimeters
        /// </summary>
        [ObservableProperty]
        private double _thickness;

        /// <summary>
        /// Wall type or floor type name
        /// </summary>
        [ObservableProperty]
        private string _typeName = string.Empty;

        /// <summary>
        /// Family name (for walls/floors)
        /// </summary>
        [ObservableProperty]
        private string _familyName = string.Empty;

        /// <summary>
        /// Level name where the element is located
        /// </summary>
        [ObservableProperty]
        private string _levelName = string.Empty;

        /// <summary>
        /// Location point in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private XYZ _locationPoint;

        /// <summary>
        /// Bounding box in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private BoundingBoxXYZ _boundingBox;

        /// <summary>
        /// Solid geometry in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private Solid _transformedSolid;

        /// <summary>
        /// Transform used to convert from linked model coordinates to host model coordinates.
        /// Used for debugging coordinate system issues with different Internal Origins.
        /// </summary>
        [ObservableProperty]
        private Transform _coordinateTransform;

        /// <summary>
        /// Name of the source linked model for coordinate system tracking.
        /// Used to identify potential coordinate system misalignments.
        /// </summary>
        [ObservableProperty]
        private string _sourceModelName = string.Empty;

        /// <summary>
        /// Category of the element (Walls, Floors, Ceilings, etc.)
        /// </summary>
        [ObservableProperty]
        private string _category = string.Empty;

        /// <summary>
        /// Construction type (e.g., "Concrete", "Steel Stud", "Masonry")
        /// </summary>
        [ObservableProperty]
        private string _constructionType = string.Empty;

        /// <summary>
        /// Function of the wall/floor (e.g., "Exterior", "Interior", "Foundation")
        /// </summary>
        [ObservableProperty]
        private string _function = string.Empty;

        /// <summary>
        /// Structural usage (e.g., "Bearing", "Non-bearing")
        /// </summary>
        [ObservableProperty]
        private string _structuralUsage = string.Empty;

        /// <summary>
        /// Assembly code for fire rating reference
        /// </summary>
        [ObservableProperty]
        private string _assemblyCode = string.Empty;

        /// <summary>
        /// Comments or notes about this structural element
        /// </summary>
        [ObservableProperty]
        private string _comments = string.Empty;

        /// <summary>
        /// Additional custom parameters that may be added in the future
        /// </summary>
        [ObservableProperty]
        private Dictionary<string, object> _customParameters = new();

        /// <summary>
        /// Timestamp when this element was extracted
        /// </summary>
        [ObservableProperty]
        private DateTime _extractedAt = DateTime.Now;

        /// <summary>
        /// Constructor for creating a new StructuralElement
        /// </summary>
        public StructuralElement()
        {
            ElementId = ElementId.InvalidElementId;
            LocationPoint = XYZ.Zero;
            StructureType = StructureType.Unknown;
        }

        /// <summary>
        /// Constructor with basic element information
        /// </summary>
        /// <param name="elementId">Revit element ID</param>
        /// <param name="revitElement">Source Revit element</param>
        /// <param name="linkInstance">Source link instance</param>
        /// <param name="structureType">Type of structure</param>
        public StructuralElement(ElementId elementId, Element revitElement, RevitLinkInstance linkInstance, StructureType structureType)
        {
            ElementId = elementId;
            RevitElement = revitElement;
            LinkInstance = linkInstance;
            StructureType = structureType;
            LocationPoint = XYZ.Zero;
        }

        /// <summary>
        /// Gets a display name for this structural element
        /// </summary>
        public string DisplayName => !string.IsNullOrEmpty(TypeName) 
            ? $"{StructureType} - {TypeName}" 
            : $"{StructureType} - {ElementId}";

        /// <summary>
        /// Gets a formatted thickness string
        /// </summary>
        public string FormattedThickness
        {
            get
            {
                if (Thickness <= 0)
                    return "Unknown";

                return $"{Thickness:F0}mm";
            }
        }

        /// <summary>
        /// Gets a formatted fire rating with fallback
        /// </summary>
        public string FormattedFireRating
        {
            get
            {
                if (string.IsNullOrEmpty(FireRating))
                    return "Not Specified";

                return FireRating;
            }
        }

        /// <summary>
        /// Indicates if this element has a fire rating
        /// </summary>
        public bool HasFireRating => !string.IsNullOrEmpty(FireRating) && 
                                   !FireRating.Equals("Not Specified", StringComparison.OrdinalIgnoreCase) &&
                                   !FireRating.Equals("None", StringComparison.OrdinalIgnoreCase);

        /// <summary>
        /// Adds or updates a custom parameter
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <param name="value">Parameter value</param>
        public void SetCustomParameter(string parameterName, object value)
        {
            CustomParameters[parameterName] = value;
        }

        /// <summary>
        /// Gets a custom parameter value
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value or null if not found</returns>
        public T? GetCustomParameter<T>(string parameterName)
        {
            if (CustomParameters.TryGetValue(parameterName, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default(T);
        }

        /// <summary>
        /// Validates that all required parameters are present
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return ElementId != ElementId.InvalidElementId &&
                   StructureType != StructureType.Unknown &&
                   RevitElement != null &&
                   LinkInstance != null;
        }

        /// <summary>
        /// Creates a copy of this element for comparison purposes
        /// </summary>
        /// <returns>A new StructuralElement with copied values</returns>
        public StructuralElement Clone()
        {
            return new StructuralElement(ElementId, RevitElement, LinkInstance, StructureType)
            {
                MaterialType = MaterialType,
                FireRating = FireRating,
                Thickness = Thickness,
                TypeName = TypeName,
                FamilyName = FamilyName,
                LevelName = LevelName,
                LocationPoint = LocationPoint,
                BoundingBox = BoundingBox,
                TransformedSolid = TransformedSolid,
                Category = Category,
                ConstructionType = ConstructionType,
                Function = Function,
                StructuralUsage = StructuralUsage,
                AssemblyCode = AssemblyCode,
                Comments = Comments,
                CustomParameters = new Dictionary<string, object>(CustomParameters),
                ExtractedAt = ExtractedAt
            };
        }
    }

    /// <summary>
    /// Enumeration of structure types
    /// </summary>
    public enum StructureType
    {
        Unknown = 0,
        Wall = 1,
        Floor = 2,
        Ceiling = 3,
        Roof = 4,
        Foundation = 5,
        Beam = 6,
        Column = 7,
        Other = 99
    }
}
