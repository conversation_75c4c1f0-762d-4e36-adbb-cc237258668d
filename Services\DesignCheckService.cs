using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using MEP.Pacifire.Helpers;
using DocumentFormat.OpenXml.Bibliography;
using System.Windows.Media;
using System.Windows.Forms;
using System.Windows.Controls;
using DocumentFormat.OpenXml.Drawing.Wordprocessing;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Service for performing design checks on fire stopping elements.
    /// Validates spatial relationships and identifies placement issues using accurate geometry.
    /// </summary>
    public class DesignCheckService : IDesignCheckService
    {
        private readonly IGeometryHelper _geometryHelper;
        private readonly ISpatialHelper _spatialHelper;

        public event EventHandler<DesignCheckProgressEventArgs>? ProgressChanged;
        public event EventHandler<DesignCheckStatusEventArgs>? StatusChanged;

        public DesignCheckService(IGeometryHelper geometryHelper, ISpatialHelper spatialHelper)
        {
            _geometryHelper = geometryHelper ?? throw new ArgumentNullException(nameof(geometryHelper));
            _spatialHelper = spatialHelper ?? throw new ArgumentNullException(nameof(spatialHelper));

            // Set the geometric distance calculator delegate for the spatial helper
            SpatialHelper.GeometricDistanceCalculator = CalculateGeometricDistance;
        }

        /// <summary>
        /// Performs all design checks on fire stopping elements
        /// </summary>
        public IEnumerable<FireStoppingElement> PerformDesignChecks(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            IEnumerable<ServiceElement> serviceElements,
            IEnumerable<StructuralElement> structuralElements,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default)
        {
            OnStatusChanged("Starting design checks...", false, "All");
            
            var fireStoppingList = fireStoppingElements.ToList();
            var serviceList = serviceElements.ToList();
            var structuralList = structuralElements.ToList();
            
            var totalElements = fireStoppingList.Count;
            var currentElement = 0;

            // Create spatial index for performance optimization
            OnStatusChanged("Building spatial index...", false, "Optimization");
            var spatialIndex = _spatialHelper.CreateSpatialIndex(fireStoppingList, structuralList, serviceList);

            foreach (var fireStoppingElement in fireStoppingList)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                currentElement++;
                OnProgressChanged(currentElement, totalElements, $"Analyzing Fire Stopping: {currentElement}/{totalElements} elements.", fireStoppingElement.ElementId.ToString());
                Application.DoEvents(); // Allow UI to update

                try
                {
                    // Reset previous check results
                    fireStoppingElement.DesignCheckResult.Reset();

                    // Perform all checks
                    PerformAllChecksForElement(
                        fireStoppingElement,
                        fireStoppingList,
                        serviceList,
                        structuralList,
                        filterSettings,
                        spatialIndex,
                        cancellationToken);
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"Error checking element {fireStoppingElement.ElementId}: {ex.Message}", true, "Error");
                    
                    // Mark all checks as failed due to error
                    fireStoppingElement.DesignCheckResult.AddCustomCheck("ProcessingError", true, ex.Message);
                }
            }

            var failureCount = fireStoppingList.Count(x => x.HasFailures);
            OnStatusChanged($"Design checks completed. {failureCount} elements have failures.", false, "Complete");
            
            return fireStoppingList;
        }

        /// <summary>
        /// Performs the "Not Touching Wall" check for a single element
        /// </summary>
        public bool CheckNotTouchingWall(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements)
        {
            try
            {
                foreach (var structure in structuralElements)
                {
                    if (structure == null) continue;

                    // If either element doesn't have geometry, fall back to center-point distance
                    if (structure == null || structure.TransformedSolid == null || fireStoppingElement.TransformedSolid == null)
                    {
                        continue;
                    }

                    // First check if they intersect (distance = 0)
                    var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                        fireStoppingElement.TransformedSolid,
                        structure.TransformedSolid,
                        BooleanOperationsType.Intersect);

                    if (intersection != null && intersection.Volume > 1e-6)
                    {
                        fireStoppingElement.AdjacentStructure = structure;
                        fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingWall",
                            $"Element intersects with {structure.StructureType}: {structure.DisplayName}");
                        return false; // Pass the check (element IS touching wall)
                    }
                }
            }
            catch (Exception ex)
            {
                // Log but continue checking other elements
                System.Diagnostics.Debug.WriteLine($"Intersection check failed: {ex.Message}");
            }

            // No intersection found
            var message = "No intersecting structural elements found";

            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingWall", message);
            return true; // Fail the check (element is NOT touching wall)
        }

        /// <summary>
        /// Validates that elements are in the same coordinate system for accurate intersection checks.
        /// This method helps identify potential issues with Internal Origin differences.
        /// </summary>
        /// <param name="fireElement">Fire stopping element</param>
        /// <param name="structuralElements">Structural elements to validate against</param>
        /// <returns>True if coordinate systems appear consistent, false otherwise</returns>
        private bool ValidateCoordinateSystemConsistency(
            FireStoppingElement fireElement,
            IEnumerable<StructuralElement> structuralElements)
        {
            try
            {
                // Check if fire element has coordinate tracking information
                if (fireElement.CoordinateTransform == null)
                {
                    System.Diagnostics.Debug.WriteLine("Warning: Fire element missing coordinate transform information");
                    return true; // Assume valid if no tracking info
                }

                // Check for potential coordinate system misalignments
                var fireOrigin = fireElement.CoordinateTransform.Origin;
                var inconsistentElements = 0;
                var totalElements = 0;

                foreach (var structural in structuralElements)
                {
                    totalElements++;

                    if (structural.CoordinateTransform == null) continue;

                    var structuralOrigin = structural.CoordinateTransform.Origin;
                    var originDistance = fireOrigin.DistanceTo(structuralOrigin);

                    // If transforms have very different origins, they might be from different coordinate systems
                    if (originDistance > 1000.0) // 1000mm = 1m threshold for different origins
                    {
                        inconsistentElements++;
                        System.Diagnostics.Debug.WriteLine(
                            $"Potential coordinate mismatch: Fire element from '{fireElement.SourceModelName}' " +
                            $"vs Structural element from '{structural.SourceModelName}' " +
                            $"(Origin distance: {originDistance:F1}mm)");
                    }
                }

                // If more than 50% of elements have inconsistent coordinates, flag as potential issue
                var inconsistencyRatio = totalElements > 0 ? (double)inconsistentElements / totalElements : 0;
                if (inconsistencyRatio > 0.5)
                {
                    System.Diagnostics.Debug.WriteLine(
                        $"High coordinate inconsistency detected: {inconsistentElements}/{totalElements} elements " +
                        $"({inconsistencyRatio:P0}) may be in different coordinate systems");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error validating coordinate system consistency: {ex.Message}");
                return true; // Assume valid on error
            }
        }

        /// <summary>
        /// Performs the "Not Touching Service" check for a single element
        /// </summary>
        public bool CheckNotTouchingService(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements)
        {
            if (fireStoppingElement.BoundingBox == null)
            {
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService", "No bounding box available for fire stopping element");
                return true; // Fail the check
            }

            try
            {
                // Get the bounding box of the fire stopping element and scale it by 0.2
                var originalBBox = fireStoppingElement.BoundingBox;
                var scaledBBox = ScaleBoundingBox(originalBBox, 1.2);

                // Check each service element
                foreach (var service in serviceElements)
                {
                    if (service == null) continue;

                    try
                    {
                        // Check if LocationPoint is inside the scaled bounding box
                        if (service.LocationPoint != null && IsPointInsideBoundingBox(service.LocationPoint, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service LocationPoint inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }

                        // Check if EndPoint1 is inside the scaled bounding box
                        if (service.EndPoint1 != null && IsPointInsideBoundingBox(service.EndPoint1, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service EndPoint1 inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }

                        // Check if EndPoint2 is inside the scaled bounding box
                        if (service.EndPoint2 != null && IsPointInsideBoundingBox(service.EndPoint2, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service EndPoint2 inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Service point check failed for element {service.ElementId}: {ex.Message}");
                        continue;
                    }
                }

                // If no service points found inside the bounding box, fail the check
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                    "No service elements found within fire stopping area");
                return true; // Fail the check
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CheckNotTouchingService failed: {ex.Message}");
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                    $"Bounding box check failed: {ex.Message}");
                return true; // Fail the check
            }
        }

        /// <summary>
        /// Performs the "Clashing" check for a single element
        /// </summary>
        public (bool IsClashing, IEnumerable<ElementId> ClashingElements) CheckClashing(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements)
        {
            var clashingElements = new List<ElementId>();

            if (fireStoppingElement.TransformedSolid == null)
            {
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Clashing", "No geometry available for clash check");
                return (false, clashingElements);
            }

            foreach (var otherElement in otherFireStoppingElements)
            {
                // Skip self
                if (otherElement.ElementId == fireStoppingElement.ElementId) continue;

                if (otherElement.TransformedSolid == null) continue;

                try
                {
                    var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                        fireStoppingElement.TransformedSolid,
                        otherElement.TransformedSolid,
                        BooleanOperationsType.Intersect);

                    if (intersection != null && intersection.Volume > 1e-6)
                    {
                        clashingElements.Add(otherElement.ElementId);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Clash check failed: {ex.Message}");
                }
            }

            var isClashing = clashingElements.Count > 0;
            if (isClashing)
            {
                fireStoppingElement.DesignCheckResult.ClashingElements = clashingElements.ToList();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Clashing",
                    $"Element clashes with {clashingElements.Count} other fire stopping element(s)");
            }

            return (isClashing, clashingElements);
        }

        /// <summary>
        /// Performs the "Adjacent" check for a single element (legacy method for backward compatibility)
        /// </summary>
        public (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements) CheckAdjacent(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements,
            double adjacencyThreshold = 300.0)
        {
            var adjacentElements = new List<ElementId>();
            var distances = new List<double>();

            foreach (var otherElement in otherFireStoppingElements)
            {
                // Skip self
                if (otherElement.ElementId == fireStoppingElement.ElementId) continue;

                // Calculates the geometric distance between two fire stopping elements (surface-to-surface)
                // 1. Checks for intersection (distance = 0)
                // 2. Samples points on solid surfaces
                // 3. Finds minimum distance between surfaces
                // 4. Falls back to center-point if needed
                var distance = CalculateGeometricDistance(fireStoppingElement, otherElement);
                if (distance <= adjacencyThreshold && distance > 1e-6) // Exclude exact overlaps (clashes)
                {
                    adjacentElements.Add(otherElement.ElementId);
                    distances.Add(distance);
                }
            }

            // Update nearest fire stopping distance
            if (distances.Count > 0)
            {
                fireStoppingElement.DesignCheckResult.DistanceToNearestFireStopping = distances.Min();
            }

            var isAdjacent = adjacentElements.Count > 0;
            if (isAdjacent)
            {
                fireStoppingElement.DesignCheckResult.AdjacentElements = adjacentElements.ToList();
                var minDistance = distances.Min();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Adjacent",
                    $"Element is within {adjacencyThreshold}mm of {adjacentElements.Count} other fire stopping element(s). Nearest surface distance: {minDistance:F1}mm");
            }

            return (isAdjacent, adjacentElements);
        }

        /// <summary>
        /// Performs the "Adjacent" check for a single element using optimized spatial indexing
        /// </summary>
        public (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements) CheckAdjacentOptimized(
            FireStoppingElement fireStoppingElement,
            SpatialIndex spatialIndex,
            double adjacencyThreshold = 300.0)
        {
            // Use the optimized spatial helper method
            var result = _spatialHelper.CheckAdjacentOptimized(spatialIndex, fireStoppingElement, adjacencyThreshold);

            // Update the element's design check result
            if (result.NearestDistance != double.MaxValue)
            {
                fireStoppingElement.DesignCheckResult.DistanceToNearestFireStopping = result.NearestDistance;
            }

            if (result.IsAdjacent)
            {
                fireStoppingElement.DesignCheckResult.AdjacentElements = result.AdjacentElements.ToList();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Adjacent",
                    $"Element is within {adjacencyThreshold}mm of {result.AdjacentElements.Count()} other fire stopping element(s). Nearest surface distance: {result.NearestDistance:F1}mm");
            }

            return (result.IsAdjacent, result.AdjacentElements);
        }

        /// <summary>
        /// Calculates the distance between two fire stopping elements
        /// </summary>
        public double CalculateDistance(FireStoppingElement element1, FireStoppingElement element2)
        {
            return CalculateDistance(element1.LocationPoint, element2.LocationPoint);
        }

        /// <summary>
        /// Runs performance analysis comparing legacy and optimized adjacency check methods
        /// </summary>
        public string AnalyzeAdjacencyPerformance(IEnumerable<FireStoppingElement> fireStoppingElements, double adjacencyThreshold = 300.0)
        {
            var comparison = PerformanceAnalyzer.CompareAdjacencyMethods(fireStoppingElements, this, _spatialHelper, adjacencyThreshold);
            var report = PerformanceAnalyzer.GeneratePerformanceReport(comparison);

            // Add scalability analysis
            var scalabilityReport = PerformanceAnalyzer.EstimateScalability(comparison, new[] { 100, 500, 1000, 2000, 5000 });

            return report + "\n" + scalabilityReport;
        }

        /// <summary>
        /// Finds the nearest structural element to a fire stopping element
        /// </summary>
        public (StructuralElement? NearestElement, double Distance) FindNearestStructuralElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements)
        {
            StructuralElement? nearestElement = null;
            var minDistance = double.MaxValue;

            foreach (var structural in structuralElements)
            {
                var distance = CalculateDistance(fireStoppingElement.LocationPoint, structural.LocationPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestElement = structural;
                }
            }

            return (nearestElement, minDistance);
        }

        /// <summary>
        /// Finds the nearest service element to a fire stopping element
        /// </summary>
        public (ServiceElement? NearestElement, double Distance) FindNearestServiceElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements)
        {
            ServiceElement? nearestElement = null;
            var minDistance = double.MaxValue;

            foreach (var service in serviceElements)
            {
                var distanceToStart = fireStoppingElement.LocationPoint.DistanceTo(service.EndPoint1);
                var distanceToEnd = fireStoppingElement.LocationPoint.DistanceTo(service.EndPoint2);
                var closestEndPoint = distanceToStart <= distanceToEnd ? service.EndPoint1 : service.EndPoint2;

                var distance = CalculateDistance(fireStoppingElement.LocationPoint, closestEndPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestElement = service;
                }
            }

            return (nearestElement, minDistance);
        }

        /// <summary>
        /// Gets detailed information about the closest service endpoint to a fire stopping element
        /// </summary>
        /// <param name="fireStoppingElement">The fire stopping element</param>
        /// <param name="serviceElement">The service element to analyze</param>
        /// <returns>Tuple containing (closest endpoint, distance to closest endpoint, is endpoint 1)</returns>
        public (XYZ ClosestEndpoint, double Distance, bool IsEndpoint1) GetClosestServiceEndpoint(
            FireStoppingElement fireStoppingElement, ServiceElement serviceElement)
        {
            if (fireStoppingElement?.LocationPoint == null ||
                serviceElement?.EndPoint1 == null ||
                serviceElement?.EndPoint2 == null)
            {
                return (XYZ.Zero, double.MaxValue, false);
            }

            var fireStoppingLocation = fireStoppingElement.LocationPoint;
            var distanceToEndPoint1 = CalculateDistance(fireStoppingLocation, serviceElement.EndPoint1);
            var distanceToEndPoint2 = CalculateDistance(fireStoppingLocation, serviceElement.EndPoint2);

            if (distanceToEndPoint1 <= distanceToEndPoint2)
            {
                return (serviceElement.EndPoint1, distanceToEndPoint1, true);
            }
            else
            {
                return (serviceElement.EndPoint2, distanceToEndPoint2, false);
            }
        }

        #region Private Helper Methods

        private void PerformAllChecksForElement(
            FireStoppingElement fireStoppingElement,
            List<FireStoppingElement> allFireStoppingElements,
            List<ServiceElement> serviceElements,
            List<StructuralElement> structuralElements,
            FilterSettings filterSettings,
            SpatialIndex spatialIndex,
            CancellationToken cancellationToken)
        {
            // Perform all four standard checks
            fireStoppingElement.DesignCheckResult.NotTouchingWall =
                CheckNotTouchingWall(fireStoppingElement, structuralElements);

            fireStoppingElement.DesignCheckResult.NotTouchingService =
                CheckNotTouchingService(fireStoppingElement, serviceElements);

            var clashResult = CheckClashing(fireStoppingElement, allFireStoppingElements);
            fireStoppingElement.DesignCheckResult.Clashing = clashResult.IsClashing;

            // Use optimized adjacency check with spatial indexing
            var adjacentResult = CheckAdjacentOptimized(fireStoppingElement, spatialIndex, filterSettings.AdjacencyThreshold);
            fireStoppingElement.DesignCheckResult.Adjacent = adjacentResult.IsAdjacent;

            // Update timestamp
            fireStoppingElement.DesignCheckResult.CheckedAt = DateTime.Now;
        }

        private double CalculateDistance(XYZ point1, XYZ point2)
        {
            if (point1 == null || point2 == null) return double.MaxValue;

            // Convert from feet to millimeters
            return BecaRevitUtilities.RevitUnitConvertor.InternalToMm(point1.DistanceTo(point2));
        }

        /// <summary>
        /// Scales a bounding box by the specified factor around its center
        /// </summary>
        /// <param name="originalBBox">The original bounding box</param>
        /// <param name="scaleFactor">Scale factor (e.g., 0.2 for 20% of original size)</param>
        /// <returns>Scaled bounding box</returns>
        private BoundingBoxXYZ ScaleBoundingBox(BoundingBoxXYZ originalBBox, double scaleFactor)
        {
            if (originalBBox == null) return null;

            try
            {
                // Calculate the center of the bounding box
                var center = (originalBBox.Min + originalBBox.Max) / 2.0;

                // Calculate the half-dimensions of the original box
                var halfWidth = (originalBBox.Max.X - originalBBox.Min.X) / 2.0;
                var halfHeight = (originalBBox.Max.Y - originalBBox.Min.Y) / 2.0;
                var halfDepth = (originalBBox.Max.Z - originalBBox.Min.Z) / 2.0;

                // Scale the half-dimensions
                var scaledHalfWidth = halfWidth * scaleFactor;
                var scaledHalfHeight = halfHeight * scaleFactor;
                var scaledHalfDepth = halfDepth * scaleFactor;

                // Create the scaled bounding box
                var scaledBBox = new BoundingBoxXYZ
                {
                    Min = new XYZ(
                        center.X - scaledHalfWidth,
                        center.Y - scaledHalfHeight,
                        center.Z - scaledHalfDepth),
                    Max = new XYZ(
                        center.X + scaledHalfWidth,
                        center.Y + scaledHalfHeight,
                        center.Z + scaledHalfDepth)
                };

                return scaledBBox;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error scaling bounding box: {ex.Message}");
                return originalBBox; // Return original if scaling fails
            }
        }

        /// <summary>
        /// Checks if a point is inside a bounding box
        /// </summary>
        /// <param name="point">The point to check</param>
        /// <param name="boundingBox">The bounding box</param>
        /// <returns>True if point is inside the bounding box</returns>
        private bool IsPointInsideBoundingBox(XYZ point, BoundingBoxXYZ boundingBox)
        {
            if (point == null || boundingBox == null) return false;

            try
            {
                return point.X >= boundingBox.Min.X && point.X <= boundingBox.Max.X &&
                       point.Y >= boundingBox.Min.Y && point.Y <= boundingBox.Max.Y &&
                       point.Z >= boundingBox.Min.Z && point.Z <= boundingBox.Max.Z;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking point inside bounding box: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Calculates the geometric distance between two fire stopping elements (surface-to-surface)
        /// 1. Intersection Check: First checks if solids intersect(distance = 0)
        /// 2. Geometric Calculation: Uses actual solid geometry for distance
        /// 3. Fallback Safety: Falls back to center-point if geometry unavailable
        /// 4. Error Handling: Graceful handling of geometric calculation failures
        /// </summary>
        private double CalculateGeometricDistance(FireStoppingElement element1, FireStoppingElement element2)
        {
            // If either element doesn't have geometry, fall back to center-point distance
            if (element1.TransformedSolid == null || element2.TransformedSolid == null)
            {
                return CalculateDistance(element1, element2);
            }

            try
            {
                // First check if they intersect (distance = 0)
                var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                    element1.TransformedSolid,
                    element2.TransformedSolid,
                    BooleanOperationsType.Intersect);

                if (intersection != null && intersection.Volume > 1e-6)
                {
                    return 0.0; // Elements are touching/overlapping
                }

                // Calculate minimum distance between the two solids
                return CalculateMinimumDistanceBetweenSolids(element1.TransformedSolid, element2.TransformedSolid);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Geometric distance calculation failed: {ex.Message}");
                // Fall back to center-point distance if geometric calculation fails
                return CalculateDistance(element1, element2);
            }
        }

        /// <summary>
        /// Calculates the minimum distance between two solids by sampling points on their surfaces
        /// 1. Face Sampling: Samples points on faces of both solids
        /// 2. Surface Projection: Projects points onto opposite solid faces
        /// 3. Minimum Distance: Finds the shortest surface-to-surface distance
        /// 4. Efficient Sampling: Uses 3�3 grid(9 points per face) for performance
        /// </summary>
        private double CalculateMinimumDistanceBetweenSolids(Solid solid1, Solid solid2)
        {
            var minDistance = double.MaxValue;

            try
            {
                // Get faces from both solids
                var faces1 = solid1.Faces.Cast<Face>().ToList();
                var faces2 = solid2.Faces.Cast<Face>().ToList();

                // Sample points on faces of solid1 and find closest points on solid2
                foreach (var face1 in faces1)
                {
                    // Sample points on this face
                    var samplePoints = SamplePointsOnFace(face1, 3); // 3x3 grid = 9 points per face

                    foreach (var point1 in samplePoints)
                    {
                        // Find closest point on solid2
                        foreach (var face2 in faces2)
                        {
                            var closestPoint = face2.Project(point1);
                            if (closestPoint != null)
                            {
                                var distance = point1.DistanceTo(closestPoint.XYZPoint) * 304.8; // Convert to mm

                                if (distance < minDistance)
                                {
                                    minDistance = distance;
                                }
                            }
                        }
                    }
                }

                return minDistance == double.MaxValue ? 0.0 : minDistance;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Solid distance calculation failed: {ex.Message}");
                return 0.0; // Assume touching if calculation fails
            }
        }

        /// <summary>
        /// Samples points on a face surface in a grid pattern
        /// 1. Grid Sampling: Creates uniform grid of points on face surface
        /// 2. UV Parameter Mapping: Uses face UV coordinates for accurate sampling
        /// 3. Boundary Checking: Ensures points are within face boundaries
        /// 4. Fallback Center: Uses face center if grid sampling fails
        /// </summary>
        private List<XYZ> SamplePointsOnFace(Face face, int gridSize = 3)
        {
            var points = new List<XYZ>();

            try
            {
                var bbox = face.GetBoundingBox();
                if (bbox == null) return points;

                // Create a grid of UV parameters
                for (int i = 0; i <= gridSize; i++)
                {
                    for (int j = 0; j <= gridSize; j++)
                    {
                        var u = bbox.Min.U + (bbox.Max.U - bbox.Min.U) * i / gridSize;
                        var v = bbox.Min.V + (bbox.Max.V - bbox.Min.V) * j / gridSize;

                        var uv = new UV(u, v);

                        if (face.IsInside(uv))
                        {
                            var point = face.Evaluate(uv);
                            points.Add(point);
                        }
                    }
                }

                // If no points found (shouldn't happen), add center point
                if (points.Count == 0)
                {
                    var centerUV = new UV(
                        (bbox.Min.U + bbox.Max.U) / 2,
                        (bbox.Min.V + bbox.Max.V) / 2);
                    points.Add(face.Evaluate(centerUV));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Face sampling failed: {ex.Message}");
                // Return empty list if sampling fails
            }

            return points;
        }

        private void OnProgressChanged(int current, int total, string operation, string elementId)
        {
            ProgressChanged?.Invoke(this, new DesignCheckProgressEventArgs
            {
                Current = current,
                Total = total,
                CurrentOperation = operation,
                ElementId = elementId
            });
        }

        private void OnStatusChanged(string status, bool isError, string checkType, Exception? exception = null)
        {
            StatusChanged?.Invoke(this, new DesignCheckStatusEventArgs
            {
                Status = status,
                IsError = isError,
                CheckType = checkType,
                Exception = exception
            });
        }

        #endregion
    }
}
