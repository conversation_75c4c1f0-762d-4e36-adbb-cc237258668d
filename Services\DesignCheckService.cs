using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using MEP.Pacifire.Helpers;
using DocumentFormat.OpenXml.Bibliography;
using System.Windows.Media;
using System.Windows.Forms;
using System.Windows.Controls;
using DocumentFormat.OpenXml.Drawing.Wordprocessing;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Service for performing design checks on fire stopping elements.
    /// Validates spatial relationships and identifies placement issues using accurate geometry.
    /// </summary>
    public class DesignCheckService : IDesignCheckService
    {
        private readonly IGeometryHelper _geometryHelper;
        private readonly ISpatialHelper _spatialHelper;

        public event EventHandler<DesignCheckProgressEventArgs>? ProgressChanged;
        public event EventHandler<DesignCheckStatusEventArgs>? StatusChanged;

        public DesignCheckService(IGeometryHelper geometryHelper, ISpatialHelper spatialHelper)
        {
            _geometryHelper = geometryHelper ?? throw new ArgumentNullException(nameof(geometryHelper));
            _spatialHelper = spatialHelper ?? throw new ArgumentNullException(nameof(spatialHelper));

            // Set the geometric distance calculator delegate for the spatial helper
            SpatialHelper.GeometricDistanceCalculator = CalculateGeometricDistance;
        }

        /// <summary>
        /// Performs all design checks on fire stopping elements
        /// </summary>
        public IEnumerable<FireStoppingElement> PerformDesignChecks(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            IEnumerable<ServiceElement> serviceElements,
            IEnumerable<StructuralElement> structuralElements,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default)
        {
            OnStatusChanged("Starting design checks...", false, "All");
            
            var fireStoppingList = fireStoppingElements.ToList();
            var serviceList = serviceElements.ToList();
            var structuralList = structuralElements.ToList();
            
            var totalElements = fireStoppingList.Count;
            var currentElement = 0;

            // Create spatial index for performance optimization
            OnStatusChanged("Building spatial index...", false, "Optimization");
            var spatialIndex = _spatialHelper.CreateSpatialIndex(fireStoppingList, structuralList, serviceList);

            foreach (var fireStoppingElement in fireStoppingList)
            {
                cancellationToken.ThrowIfCancellationRequested();
                
                currentElement++;
                OnProgressChanged(currentElement, totalElements, $"Analyzing Fire Stopping: {currentElement}/{totalElements} elements.", fireStoppingElement.ElementId.ToString());
                Application.DoEvents(); // Allow UI to update

                try
                {
                    // Reset previous check results
                    fireStoppingElement.DesignCheckResult.Reset();

                    // Perform all checks
                    PerformAllChecksForElement(
                        fireStoppingElement,
                        fireStoppingList,
                        serviceList,
                        structuralList,
                        filterSettings,
                        spatialIndex,
                        cancellationToken);
                }
                catch (Exception ex)
                {
                    OnStatusChanged($"Error checking element {fireStoppingElement.ElementId}: {ex.Message}", true, "Error");
                    
                    // Mark all checks as failed due to error
                    fireStoppingElement.DesignCheckResult.AddCustomCheck("ProcessingError", true, ex.Message);
                }
            }

            var failureCount = fireStoppingList.Count(x => x.HasFailures);
            OnStatusChanged($"Design checks completed. {failureCount} elements have failures.", false, "Complete");
            
            return fireStoppingList;
        }

        /// <summary>
        /// Performs the "Not Touching Wall" check for a single element
        /// </summary>
        public bool CheckNotTouchingWall(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements)
        {
try
{
    foreach (var structuralElement in structuralElements)
    {
        if (structuralElement?.TransformedSolid == null || fireStoppingElement.TransformedSolid == null)
            continue;

        try
        {
            // Debug logging for coordinate analysis
            if (fireStoppingElement.LocationPoint != null && structuralElement.BoundingBox != null)
            {
                var originalPoint = fireStoppingElement.LocationPoint;
                var originalBbox = structuralElement.BoundingBox;

                // COORDINATE SYSTEM NORMALIZATION
                // If elements have different transforms, normalize them to the same coordinate system
                var point = originalPoint;
                var bbox = originalBbox;

                // Check if transforms are different (indicating different coordinate systems)
                var fireTransform = fireStoppingElement.CoordinateTransform;
                var structTransform = structuralElement.CoordinateTransform;

                if (fireTransform != null && structTransform != null)
                {
                    // If fire element has identity transform but structural doesn't, apply structural transform to fire point
                    if (IsIdentityTransform(fireTransform) && !IsIdentityTransform(structTransform))
                    {
                        // Fire element is in host coordinates, structural is in linked coordinates
                        // We need to transform the fire point to the structural element's coordinate system
                        var inverseStructTransform = structTransform.Inverse;
                        point = inverseStructTransform.OfPoint(originalPoint);
                        System.Diagnostics.Debug.WriteLine($"Applied inverse structural transform to fire point");
                        System.Diagnostics.Debug.WriteLine($"Original point: ({originalPoint.X:F6}, {originalPoint.Y:F6}, {originalPoint.Z:F6})");
                        System.Diagnostics.Debug.WriteLine($"Transformed point: ({point.X:F6}, {point.Y:F6}, {point.Z:F6})");
                    }
                    // If structural has identity transform but fire doesn't, apply fire transform to structural bbox
                    else if (!IsIdentityTransform(fireTransform) && IsIdentityTransform(structTransform))
                    {
                        // Structural element is in host coordinates, fire is in linked coordinates
                        // Transform structural bbox to fire element's coordinate system
                        var inverseFireTransform = fireTransform.Inverse;
                        bbox = TransformBoundingBox(originalBbox, inverseFireTransform);
                        System.Diagnostics.Debug.WriteLine($"Applied inverse fire transform to structural bbox");
                    }
                    // If both have non-identity transforms, normalize both to host coordinates
                    else if (!IsIdentityTransform(fireTransform) && !IsIdentityTransform(structTransform))
                    {
                        // Transform both to host coordinates
                        point = fireTransform.OfPoint(GetOriginalPoint(fireStoppingElement));
                        bbox = TransformBoundingBox(GetOriginalBoundingBox(structuralElement), structTransform);
                        System.Diagnostics.Debug.WriteLine($"Normalized both elements to host coordinates");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"=== COORDINATE DEBUG ===");
                System.Diagnostics.Debug.WriteLine($"Fire Element: {fireStoppingElement.DisplayName}");
                System.Diagnostics.Debug.WriteLine($"Structural Element: {structuralElement.DisplayName}");
                System.Diagnostics.Debug.WriteLine($"Fire Model: {fireStoppingElement.SourceModelName}");
                System.Diagnostics.Debug.WriteLine($"Structural Model: {structuralElement.SourceModelName}");

                // Log transform information for debugging
                if (fireStoppingElement.CoordinateTransform != null)
                {
                    var fireTransform = fireStoppingElement.CoordinateTransform;
                    System.Diagnostics.Debug.WriteLine($"Fire Transform Origin: ({fireTransform.Origin.X:F6}, {fireTransform.Origin.Y:F6}, {fireTransform.Origin.Z:F6})");
                }
                if (structuralElement.CoordinateTransform != null)
                {
                    var structTransform = structuralElement.CoordinateTransform;
                    System.Diagnostics.Debug.WriteLine($"Struct Transform Origin: ({structTransform.Origin.X:F6}, {structTransform.Origin.Y:F6}, {structTransform.Origin.Z:F6})");
                }

                System.Diagnostics.Debug.WriteLine($"Point: ({point.X:F6}, {point.Y:F6}, {point.Z:F6})");
                System.Diagnostics.Debug.WriteLine($"BBox Min: ({bbox.Min.X:F6}, {bbox.Min.Y:F6}, {bbox.Min.Z:F6})");
                System.Diagnostics.Debug.WriteLine($"BBox Max: ({bbox.Max.X:F6}, {bbox.Max.Y:F6}, {bbox.Max.Z:F6})");

                // Check each dimension
                bool xInside = point.X >= bbox.Min.X && point.X <= bbox.Max.X;
                bool yInside = point.Y >= bbox.Min.Y && point.Y <= bbox.Max.Y;
                bool zInside = point.Z >= bbox.Min.Z && point.Z <= bbox.Max.Z;

                System.Diagnostics.Debug.WriteLine($"X Inside: {xInside} (diff from min: {point.X - bbox.Min.X:F6}, diff from max: {bbox.Max.X - point.X:F6})");
                System.Diagnostics.Debug.WriteLine($"Y Inside: {yInside} (diff from min: {point.Y - bbox.Min.Y:F6}, diff from max: {bbox.Max.Y - point.Y:F6})");
                System.Diagnostics.Debug.WriteLine($"Z Inside: {zInside} (diff from min: {point.Z - bbox.Min.Z:F6}, diff from max: {bbox.Max.Z - point.Z:F6})");

                // Try exact bounding box first
                if (IsPointInsideBoundingBox(point, bbox))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ POINT IS INSIDE BOUNDING BOX (EXACT)");
                    fireStoppingElement.AdjacentStructure = structuralElement;
                    fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingStructure",
                        $"Touching with {structuralElement.DisplayName}");
                    return false; // Element IS touching a structure
                }

                // Try with expanded bounding box (tolerance for coordinate misalignment)
                var expandedBBox = ExpandBoundingBox(bbox, 0.2); // 0.2 feet = ~61mm tolerance
                if (IsPointInsideBoundingBox(point, expandedBBox))
                {
                    System.Diagnostics.Debug.WriteLine($"✅ POINT IS INSIDE EXPANDED BOUNDING BOX (TOLERANCE)");
                    fireStoppingElement.AdjacentStructure = structuralElement;
                    fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingStructure",
                        $"Touching with {structuralElement.DisplayName} (tolerance match)");
                    return false; // Element IS touching a structure
                }

                // Try proximity-based detection as final fallback
                var bboxCenter = new XYZ(
                    (bbox.Min.X + bbox.Max.X) / 2,
                    (bbox.Min.Y + bbox.Max.Y) / 2,
                    (bbox.Min.Z + bbox.Max.Z) / 2);
                var distance = point.DistanceTo(bboxCenter);
                var distanceMm = distance * 304.8; // Convert to mm

                System.Diagnostics.Debug.WriteLine($"Distance to bbox center: {distance:F6} feet ({distanceMm:F1} mm)");

                if (distanceMm < 200) // Within 200mm - reasonable for fire stopping elements
                {
                    System.Diagnostics.Debug.WriteLine($"✅ POINT IS WITHIN PROXIMITY TOLERANCE ({distanceMm:F1}mm)");
                    fireStoppingElement.AdjacentStructure = structuralElement;
                    fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingStructure",
                        $"Near {structuralElement.DisplayName} (distance: {distanceMm:F1}mm)");
                    return false; // Element IS touching a structure
                }

                System.Diagnostics.Debug.WriteLine($"❌ POINT IS OUTSIDE ALL DETECTION METHODS");
                System.Diagnostics.Debug.WriteLine($"========================");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error checking intersection with {structuralElement.DisplayName}: {ex.Message}");
        }
    }

    // No intersection found
    fireStoppingElement.AdjacentStructure = null;
    fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingStructure",
        "No adjacent structural elements found");
    return true; // Element is NOT touching a structure
}
catch (Exception ex)
{
    System.Diagnostics.Debug.WriteLine($"Error in CheckNotTouchingStructure: {ex.Message}");
    fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingStructure",
        $"Error during check: {ex.Message}");
    return false;
}
        }



        /// <summary>
        /// Enhanced intersection detection using multiple methods to handle edge cases
        /// </summary>
        private bool CheckElementsIntersect(FireStoppingElement fireElement, StructuralElement structuralElement)
        {
            try
            {
                // Method 1: Standard Boolean intersection with relaxed tolerance
                var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                    fireElement.TransformedSolid,
                    structuralElement.TransformedSolid,
                    BooleanOperationsType.Intersect);

                // Check for meaningful intersection (volume > small tolerance)
                if (intersection != null && intersection.Volume > 1e-9) // Very small but non-zero tolerance
                {
                    System.Diagnostics.Debug.WriteLine($"Boolean intersection found - Volume: {intersection.Volume:E6} between " +
                        $"Fire Element '{fireElement.DisplayName}' and Structural '{structuralElement.DisplayName}'");
                    return true;
                }

                // Method 2: Check for zero-volume intersection ONLY if elements are geometrically close
                if (intersection != null && intersection.Volume == 0)
                {
                    // Additional validation: only consider zero-volume as contact if elements are very close
                    var centerDistance = CalculateDistance(fireElement.LocationPoint, structuralElement.LocationPoint);
                    if (centerDistance < 100.0) // Only within 100mm
                    {
                        System.Diagnostics.Debug.WriteLine($"Surface contact detected (zero-volume intersection, centers within {centerDistance:F2}mm) between " +
                            $"Fire Element '{fireElement.DisplayName}' and Structural '{structuralElement.DisplayName}'");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"Zero-volume intersection ignored (centers too far: {centerDistance:F2}mm) between " +
                            $"Fire Element '{fireElement.DisplayName}' and Structural '{structuralElement.DisplayName}'");
                    }
                }

                // Log when no boolean intersection is found
                if (intersection == null)
                {
                    System.Diagnostics.Debug.WriteLine($"No boolean intersection between " +
                        $"Fire Element '{fireElement.DisplayName}' and Structural '{structuralElement.DisplayName}'");
                }

                // Method 3: Bounding box proximity check as fallback
                if (fireElement.BoundingBox != null && structuralElement.BoundingBox != null)
                {
                    var boundingBoxDistance = CalculateBoundingBoxDistance(fireElement.BoundingBox, structuralElement.BoundingBox);

                    // If bounding boxes are very close (within 10mm), consider as potential intersection
                    if (boundingBoxDistance < 10.0) // 10mm tolerance
                    {
                        System.Diagnostics.Debug.WriteLine($"Close proximity detected - Bounding box distance: {boundingBoxDistance:F2}mm");
                        return true;
                    }
                }

                // Method 4: Center point distance check as final fallback
                var centerDistance = CalculateDistance(fireElement.LocationPoint, structuralElement.LocationPoint);
                if (centerDistance < 50.0) // 50mm tolerance for center points
                {
                    System.Diagnostics.Debug.WriteLine($"Close center points detected - Distance: {centerDistance:F2}mm");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Enhanced intersection check failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Calculates the minimum distance between two bounding boxes
        /// </summary>
        private double CalculateBoundingBoxDistance(BoundingBoxXYZ box1, BoundingBoxXYZ box2)
        {
            if (box1 == null || box2 == null) return double.MaxValue;

            // Check if boxes intersect
            if (!(box1.Max.X < box2.Min.X || box2.Max.X < box1.Min.X ||
                  box1.Max.Y < box2.Min.Y || box2.Max.Y < box1.Min.Y ||
                  box1.Max.Z < box2.Min.Z || box2.Max.Z < box1.Min.Z))
            {
                return 0.0; // Boxes intersect
            }

            // Calculate minimum distance between boxes
            var dx = Math.Max(0, Math.Max(box1.Min.X - box2.Max.X, box2.Min.X - box1.Max.X));
            var dy = Math.Max(0, Math.Max(box1.Min.Y - box2.Max.Y, box2.Min.Y - box1.Max.Y));
            var dz = Math.Max(0, Math.Max(box1.Min.Z - box2.Max.Z, box2.Min.Z - box1.Max.Z));

            var distanceFeet = Math.Sqrt(dx * dx + dy * dy + dz * dz);
            return BecaRevitUtilities.RevitUnitConvertor.InternalToMm(distanceFeet);
        }

        /// <summary>
        /// Validates that elements are in the same coordinate system for accurate intersection checks.
        /// This method helps identify potential issues with Internal Origin differences.
        /// </summary>
        /// <param name="fireElement">Fire stopping element</param>
        /// <param name="structuralElements">Structural elements to validate against</param>
        /// <returns>True if coordinate systems appear consistent, false otherwise</returns>
        private bool ValidateCoordinateSystemConsistency(
            FireStoppingElement fireElement,
            IEnumerable<StructuralElement> structuralElements)
        {
            try
            {
                // Check if fire element has coordinate tracking information
                if (fireElement.CoordinateTransform == null)
                {
                    System.Diagnostics.Debug.WriteLine("Warning: Fire element missing coordinate transform information");
                    return true; // Assume valid if no tracking info
                }

                // Check for potential coordinate system misalignments
                var fireOrigin = fireElement.CoordinateTransform.Origin;
                var inconsistentElements = 0;
                var totalElements = 0;

                foreach (var structural in structuralElements)
                {
                    totalElements++;

                    if (structural.CoordinateTransform == null) continue;

                    var structuralOrigin = structural.CoordinateTransform.Origin;
                    var originDistance = fireOrigin.DistanceTo(structuralOrigin);

                    // If transforms have very different origins, they might be from different coordinate systems
                    if (originDistance > 1000.0) // 1000mm = 1m threshold for different origins
                    {
                        inconsistentElements++;
                        System.Diagnostics.Debug.WriteLine(
                            $"Potential coordinate mismatch: Fire element from '{fireElement.SourceModelName}' " +
                            $"vs Structural element from '{structural.SourceModelName}' " +
                            $"(Origin distance: {originDistance:F1}mm)");
                    }
                }

                // If more than 50% of elements have inconsistent coordinates, flag as potential issue
                var inconsistencyRatio = totalElements > 0 ? (double)inconsistentElements / totalElements : 0;
                if (inconsistencyRatio > 0.5)
                {
                    System.Diagnostics.Debug.WriteLine(
                        $"High coordinate inconsistency detected: {inconsistentElements}/{totalElements} elements " +
                        $"({inconsistencyRatio:P0}) may be in different coordinate systems");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error validating coordinate system consistency: {ex.Message}");
                return true; // Assume valid on error
            }
        }

        /// <summary>
        /// Performs the "Not Touching Service" check for a single element
        /// </summary>
        public bool CheckNotTouchingService(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements)
        {
            if (fireStoppingElement.BoundingBox == null)
            {
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService", "No bounding box available for fire stopping element");
                return true; // Fail the check
            }

            try
            {
                // Get the bounding box of the fire stopping element and scale it by 0.2
                var originalBBox = fireStoppingElement.BoundingBox;
                var scaledBBox = ScaleBoundingBox(originalBBox, 1.2);

                // Check each service element
                foreach (var service in serviceElements)
                {
                    if (service == null) continue;

                    try
                    {
                        // Check if LocationPoint is inside the scaled bounding box
                        if (service.LocationPoint != null && IsPointInsideBoundingBox(service.LocationPoint, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service LocationPoint inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }

                        // Check if EndPoint1 is inside the scaled bounding box
                        if (service.EndPoint1 != null && IsPointInsideBoundingBox(service.EndPoint1, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service EndPoint1 inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }

                        // Check if EndPoint2 is inside the scaled bounding box
                        if (service.EndPoint2 != null && IsPointInsideBoundingBox(service.EndPoint2, scaledBBox))
                        {
                            fireStoppingElement.ConnectedService = service;
                            fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                                $"Service EndPoint2 inside fire stopping area: {service.ServiceType} - {service.DisplayName}");
                            return false; // Pass the check (service is connected)
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Service point check failed for element {service.ElementId}: {ex.Message}");
                        continue;
                    }
                }

                // If no service points found inside the bounding box, fail the check
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                    "No service elements found within fire stopping area");
                return true; // Fail the check
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CheckNotTouchingService failed: {ex.Message}");
                fireStoppingElement.DesignCheckResult.SetCheckMessage("NotTouchingService",
                    $"Bounding box check failed: {ex.Message}");
                return true; // Fail the check
            }
        }

        /// <summary>
        /// Performs the "Clashing" check for a single element
        /// </summary>
        public (bool IsClashing, IEnumerable<ElementId> ClashingElements) CheckClashing(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements)
        {
            var clashingElements = new List<ElementId>();

            if (fireStoppingElement.TransformedSolid == null)
            {
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Clashing", "No geometry available for clash check");
                return (false, clashingElements);
            }

            foreach (var otherElement in otherFireStoppingElements)
            {
                // Skip self
                if (otherElement.ElementId == fireStoppingElement.ElementId) continue;

                if (otherElement.TransformedSolid == null) continue;

                try
                {
                    var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                        fireStoppingElement.TransformedSolid,
                        otherElement.TransformedSolid,
                        BooleanOperationsType.Intersect);

                    if (intersection != null && intersection.Volume > 1e-6)
                    {
                        clashingElements.Add(otherElement.ElementId);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Clash check failed: {ex.Message}");
                }
            }

            var isClashing = clashingElements.Count > 0;
            if (isClashing)
            {
                fireStoppingElement.DesignCheckResult.ClashingElements = clashingElements.ToList();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Clashing",
                    $"Element clashes with {clashingElements.Count} other fire stopping element(s)");
            }

            return (isClashing, clashingElements);
        }

        /// <summary>
        /// Performs the "Adjacent" check for a single element (legacy method for backward compatibility)
        /// </summary>
        public (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements) CheckAdjacent(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements,
            double adjacencyThreshold = 300.0)
        {
            var adjacentElements = new List<ElementId>();
            var distances = new List<double>();

            foreach (var otherElement in otherFireStoppingElements)
            {
                // Skip self
                if (otherElement.ElementId == fireStoppingElement.ElementId) continue;

                // Calculates the geometric distance between two fire stopping elements (surface-to-surface)
                // 1. Checks for intersection (distance = 0)
                // 2. Samples points on solid surfaces
                // 3. Finds minimum distance between surfaces
                // 4. Falls back to center-point if needed
                var distance = CalculateGeometricDistance(fireStoppingElement, otherElement);
                if (distance <= adjacencyThreshold && distance > 1e-6) // Exclude exact overlaps (clashes)
                {
                    adjacentElements.Add(otherElement.ElementId);
                    distances.Add(distance);
                }
            }

            // Update nearest fire stopping distance
            if (distances.Count > 0)
            {
                fireStoppingElement.DesignCheckResult.DistanceToNearestFireStopping = distances.Min();
            }

            var isAdjacent = adjacentElements.Count > 0;
            if (isAdjacent)
            {
                fireStoppingElement.DesignCheckResult.AdjacentElements = adjacentElements.ToList();
                var minDistance = distances.Min();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Adjacent",
                    $"Element is within {adjacencyThreshold}mm of {adjacentElements.Count} other fire stopping element(s). Nearest surface distance: {minDistance:F1}mm");
            }

            return (isAdjacent, adjacentElements);
        }

        /// <summary>
        /// Performs the "Adjacent" check for a single element using optimized spatial indexing
        /// </summary>
        public (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements) CheckAdjacentOptimized(
            FireStoppingElement fireStoppingElement,
            SpatialIndex spatialIndex,
            double adjacencyThreshold = 300.0)
        {
            // Use the optimized spatial helper method
            var result = _spatialHelper.CheckAdjacentOptimized(spatialIndex, fireStoppingElement, adjacencyThreshold);

            // Update the element's design check result
            if (result.NearestDistance != double.MaxValue)
            {
                fireStoppingElement.DesignCheckResult.DistanceToNearestFireStopping = result.NearestDistance;
            }

            if (result.IsAdjacent)
            {
                fireStoppingElement.DesignCheckResult.AdjacentElements = result.AdjacentElements.ToList();
                fireStoppingElement.DesignCheckResult.SetCheckMessage("Adjacent",
                    $"Element is within {adjacencyThreshold}mm of {result.AdjacentElements.Count()} other fire stopping element(s). Nearest surface distance: {result.NearestDistance:F1}mm");
            }

            return (result.IsAdjacent, result.AdjacentElements);
        }

        /// <summary>
        /// Calculates the distance between two fire stopping elements
        /// </summary>
        public double CalculateDistance(FireStoppingElement element1, FireStoppingElement element2)
        {
            return CalculateDistance(element1.LocationPoint, element2.LocationPoint);
        }

        /// <summary>
        /// Runs performance analysis comparing legacy and optimized adjacency check methods
        /// </summary>
        public string AnalyzeAdjacencyPerformance(IEnumerable<FireStoppingElement> fireStoppingElements, double adjacencyThreshold = 300.0)
        {
            var comparison = PerformanceAnalyzer.CompareAdjacencyMethods(fireStoppingElements, this, _spatialHelper, adjacencyThreshold);
            var report = PerformanceAnalyzer.GeneratePerformanceReport(comparison);

            // Add scalability analysis
            var scalabilityReport = PerformanceAnalyzer.EstimateScalability(comparison, new[] { 100, 500, 1000, 2000, 5000 });

            return report + "\n" + scalabilityReport;
        }

        /// <summary>
        /// Finds the nearest structural element to a fire stopping element
        /// </summary>
        public (StructuralElement? NearestElement, double Distance) FindNearestStructuralElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements)
        {
            StructuralElement? nearestElement = null;
            var minDistance = double.MaxValue;

            foreach (var structural in structuralElements)
            {
                var distance = CalculateDistance(fireStoppingElement.LocationPoint, structural.LocationPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestElement = structural;
                }
            }

            return (nearestElement, minDistance);
        }

        /// <summary>
        /// Finds the nearest service element to a fire stopping element
        /// </summary>
        public (ServiceElement? NearestElement, double Distance) FindNearestServiceElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements)
        {
            ServiceElement? nearestElement = null;
            var minDistance = double.MaxValue;

            foreach (var service in serviceElements)
            {
                var distanceToStart = fireStoppingElement.LocationPoint.DistanceTo(service.EndPoint1);
                var distanceToEnd = fireStoppingElement.LocationPoint.DistanceTo(service.EndPoint2);
                var closestEndPoint = distanceToStart <= distanceToEnd ? service.EndPoint1 : service.EndPoint2;

                var distance = CalculateDistance(fireStoppingElement.LocationPoint, closestEndPoint);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearestElement = service;
                }
            }

            return (nearestElement, minDistance);
        }

        /// <summary>
        /// Gets detailed information about the closest service endpoint to a fire stopping element
        /// </summary>
        /// <param name="fireStoppingElement">The fire stopping element</param>
        /// <param name="serviceElement">The service element to analyze</param>
        /// <returns>Tuple containing (closest endpoint, distance to closest endpoint, is endpoint 1)</returns>
        public (XYZ ClosestEndpoint, double Distance, bool IsEndpoint1) GetClosestServiceEndpoint(
            FireStoppingElement fireStoppingElement, ServiceElement serviceElement)
        {
            if (fireStoppingElement?.LocationPoint == null ||
                serviceElement?.EndPoint1 == null ||
                serviceElement?.EndPoint2 == null)
            {
                return (XYZ.Zero, double.MaxValue, false);
            }

            var fireStoppingLocation = fireStoppingElement.LocationPoint;
            var distanceToEndPoint1 = CalculateDistance(fireStoppingLocation, serviceElement.EndPoint1);
            var distanceToEndPoint2 = CalculateDistance(fireStoppingLocation, serviceElement.EndPoint2);

            if (distanceToEndPoint1 <= distanceToEndPoint2)
            {
                return (serviceElement.EndPoint1, distanceToEndPoint1, true);
            }
            else
            {
                return (serviceElement.EndPoint2, distanceToEndPoint2, false);
            }
        }

        #region Private Helper Methods

        private void PerformAllChecksForElement(
            FireStoppingElement fireStoppingElement,
            List<FireStoppingElement> allFireStoppingElements,
            List<ServiceElement> serviceElements,
            List<StructuralElement> structuralElements,
            FilterSettings filterSettings,
            SpatialIndex spatialIndex,
            CancellationToken cancellationToken)
        {
            // Perform all four standard checks
            fireStoppingElement.DesignCheckResult.NotTouchingWall =
                CheckNotTouchingWall(fireStoppingElement, structuralElements);

            fireStoppingElement.DesignCheckResult.NotTouchingService =
                CheckNotTouchingService(fireStoppingElement, serviceElements);

            var clashResult = CheckClashing(fireStoppingElement, allFireStoppingElements);
            fireStoppingElement.DesignCheckResult.Clashing = clashResult.IsClashing;

            // Use optimized adjacency check with spatial indexing
            var adjacentResult = CheckAdjacentOptimized(fireStoppingElement, spatialIndex, filterSettings.AdjacencyThreshold);
            fireStoppingElement.DesignCheckResult.Adjacent = adjacentResult.IsAdjacent;

            // Update timestamp
            fireStoppingElement.DesignCheckResult.CheckedAt = DateTime.Now;
        }

        private double CalculateDistance(XYZ point1, XYZ point2)
        {
            if (point1 == null || point2 == null) return double.MaxValue;

            // Convert from feet to millimeters
            return BecaRevitUtilities.RevitUnitConvertor.InternalToMm(point1.DistanceTo(point2));
        }

        /// <summary>
        /// Scales a bounding box by the specified factor around its center
        /// </summary>
        /// <param name="originalBBox">The original bounding box</param>
        /// <param name="scaleFactor">Scale factor (e.g., 0.2 for 20% of original size)</param>
        /// <returns>Scaled bounding box</returns>
        private BoundingBoxXYZ ScaleBoundingBox(BoundingBoxXYZ originalBBox, double scaleFactor)
        {
            if (originalBBox == null) return null;

            try
            {
                // Calculate the center of the bounding box
                var center = (originalBBox.Min + originalBBox.Max) / 2.0;

                // Calculate the half-dimensions of the original box
                var halfWidth = (originalBBox.Max.X - originalBBox.Min.X) / 2.0;
                var halfHeight = (originalBBox.Max.Y - originalBBox.Min.Y) / 2.0;
                var halfDepth = (originalBBox.Max.Z - originalBBox.Min.Z) / 2.0;

                // Scale the half-dimensions
                var scaledHalfWidth = halfWidth * scaleFactor;
                var scaledHalfHeight = halfHeight * scaleFactor;
                var scaledHalfDepth = halfDepth * scaleFactor;

                // Create the scaled bounding box
                var scaledBBox = new BoundingBoxXYZ
                {
                    Min = new XYZ(
                        center.X - scaledHalfWidth,
                        center.Y - scaledHalfHeight,
                        center.Z - scaledHalfDepth),
                    Max = new XYZ(
                        center.X + scaledHalfWidth,
                        center.Y + scaledHalfHeight,
                        center.Z + scaledHalfDepth)
                };

                return scaledBBox;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error scaling bounding box: {ex.Message}");
                return originalBBox; // Return original if scaling fails
            }
        }

        /// <summary>
        /// Checks if a point is inside a bounding box
        /// </summary>
        /// <param name="point">The point to check</param>
        /// <param name="boundingBox">The bounding box</param>
        /// <returns>True if point is inside the bounding box</returns>
        private bool IsPointInsideBoundingBox(XYZ point, BoundingBoxXYZ boundingBox)
        {
            if (point == null || boundingBox == null) return false;

            try
            {
                return point.X >= boundingBox.Min.X && point.X <= boundingBox.Max.X &&
                       point.Y >= boundingBox.Min.Y && point.Y <= boundingBox.Max.Y &&
                       point.Z >= boundingBox.Min.Z && point.Z <= boundingBox.Max.Z;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking point inside bounding box: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Expands a bounding box by the specified tolerance in all directions
        /// </summary>
        /// <param name="boundingBox">The original bounding box</param>
        /// <param name="tolerance">The tolerance to expand by (in feet)</param>
        /// <returns>An expanded bounding box</returns>
        private BoundingBoxXYZ ExpandBoundingBox(BoundingBoxXYZ boundingBox, double tolerance)
        {
            if (boundingBox == null) return null;

            try
            {
                return new BoundingBoxXYZ
                {
                    Min = new XYZ(
                        boundingBox.Min.X - tolerance,
                        boundingBox.Min.Y - tolerance,
                        boundingBox.Min.Z - tolerance),
                    Max = new XYZ(
                        boundingBox.Max.X + tolerance,
                        boundingBox.Max.Y + tolerance,
                        boundingBox.Max.Z + tolerance)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error expanding bounding box: {ex.Message}");
                return boundingBox; // Return original if expansion fails
            }
        }

        /// <summary>
        /// Checks if a transform is an identity transform (no translation or rotation)
        /// </summary>
        private bool IsIdentityTransform(Transform transform)
        {
            if (transform == null) return true;

            const double tolerance = 1e-6;
            return Math.Abs(transform.Origin.X) < tolerance &&
                   Math.Abs(transform.Origin.Y) < tolerance &&
                   Math.Abs(transform.Origin.Z) < tolerance;
        }

        /// <summary>
        /// Transforms a bounding box using the specified transform
        /// </summary>
        private BoundingBoxXYZ TransformBoundingBox(BoundingBoxXYZ boundingBox, Transform transform)
        {
            if (boundingBox == null || transform == null) return boundingBox;

            try
            {
                var transformedBox = new BoundingBoxXYZ
                {
                    Min = transform.OfPoint(boundingBox.Min),
                    Max = transform.OfPoint(boundingBox.Max)
                };

                // Ensure Min is actually minimum and Max is maximum after transformation
                var actualMin = new XYZ(
                    Math.Min(transformedBox.Min.X, transformedBox.Max.X),
                    Math.Min(transformedBox.Min.Y, transformedBox.Max.Y),
                    Math.Min(transformedBox.Min.Z, transformedBox.Max.Z)
                );

                var actualMax = new XYZ(
                    Math.Max(transformedBox.Min.X, transformedBox.Max.X),
                    Math.Max(transformedBox.Min.Y, transformedBox.Max.Y),
                    Math.Max(transformedBox.Min.Z, transformedBox.Max.Z)
                );

                return new BoundingBoxXYZ { Min = actualMin, Max = actualMax };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error transforming bounding box: {ex.Message}");
                return boundingBox;
            }
        }

        /// <summary>
        /// Gets the original untransformed point from a fire stopping element
        /// </summary>
        private XYZ GetOriginalPoint(FireStoppingElement element)
        {
            // This would need to be implemented to get the original point before transformation
            // For now, return the current point
            return element.LocationPoint;
        }

        /// <summary>
        /// Gets the original untransformed bounding box from a structural element
        /// </summary>
        private BoundingBoxXYZ GetOriginalBoundingBox(StructuralElement element)
        {
            // This would need to be implemented to get the original bbox before transformation
            // For now, return the current bounding box
            return element.BoundingBox;
        }

        /// <summary>
        /// Calculates the geometric distance between two fire stopping elements (surface-to-surface)
        /// 1. Intersection Check: First checks if solids intersect(distance = 0)
        /// 2. Geometric Calculation: Uses actual solid geometry for distance
        /// 3. Fallback Safety: Falls back to center-point if geometry unavailable
        /// 4. Error Handling: Graceful handling of geometric calculation failures
        /// </summary>
        private double CalculateGeometricDistance(FireStoppingElement element1, FireStoppingElement element2)
        {
            // If either element doesn't have geometry, fall back to center-point distance
            if (element1.TransformedSolid == null || element2.TransformedSolid == null)
            {
                return CalculateDistance(element1, element2);
            }

            try
            {
                // First check if they intersect (distance = 0)
                var intersection = BooleanOperationsUtils.ExecuteBooleanOperation(
                    element1.TransformedSolid,
                    element2.TransformedSolid,
                    BooleanOperationsType.Intersect);

                if (intersection != null && intersection.Volume > 1e-6)
                {
                    return 0.0; // Elements are touching/overlapping
                }

                // Calculate minimum distance between the two solids
                return CalculateMinimumDistanceBetweenSolids(element1.TransformedSolid, element2.TransformedSolid);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Geometric distance calculation failed: {ex.Message}");
                // Fall back to center-point distance if geometric calculation fails
                return CalculateDistance(element1, element2);
            }
        }

        /// <summary>
        /// Calculates the minimum distance between two solids by sampling points on their surfaces
        /// 1. Face Sampling: Samples points on faces of both solids
        /// 2. Surface Projection: Projects points onto opposite solid faces
        /// 3. Minimum Distance: Finds the shortest surface-to-surface distance
        /// 4. Efficient Sampling: Uses 3�3 grid(9 points per face) for performance
        /// </summary>
        private double CalculateMinimumDistanceBetweenSolids(Solid solid1, Solid solid2)
        {
            var minDistance = double.MaxValue;

            try
            {
                // Get faces from both solids
                var faces1 = solid1.Faces.Cast<Face>().ToList();
                var faces2 = solid2.Faces.Cast<Face>().ToList();

                // Sample points on faces of solid1 and find closest points on solid2
                foreach (var face1 in faces1)
                {
                    // Sample points on this face
                    var samplePoints = SamplePointsOnFace(face1, 3); // 3x3 grid = 9 points per face

                    foreach (var point1 in samplePoints)
                    {
                        // Find closest point on solid2
                        foreach (var face2 in faces2)
                        {
                            var closestPoint = face2.Project(point1);
                            if (closestPoint != null)
                            {
                                var distance = point1.DistanceTo(closestPoint.XYZPoint) * 304.8; // Convert to mm

                                if (distance < minDistance)
                                {
                                    minDistance = distance;
                                }
                            }
                        }
                    }
                }

                return minDistance == double.MaxValue ? 0.0 : minDistance;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Solid distance calculation failed: {ex.Message}");
                return 0.0; // Assume touching if calculation fails
            }
        }

        /// <summary>
        /// Samples points on a face surface in a grid pattern
        /// 1. Grid Sampling: Creates uniform grid of points on face surface
        /// 2. UV Parameter Mapping: Uses face UV coordinates for accurate sampling
        /// 3. Boundary Checking: Ensures points are within face boundaries
        /// 4. Fallback Center: Uses face center if grid sampling fails
        /// </summary>
        private List<XYZ> SamplePointsOnFace(Face face, int gridSize = 3)
        {
            var points = new List<XYZ>();

            try
            {
                var bbox = face.GetBoundingBox();
                if (bbox == null) return points;

                // Create a grid of UV parameters
                for (int i = 0; i <= gridSize; i++)
                {
                    for (int j = 0; j <= gridSize; j++)
                    {
                        var u = bbox.Min.U + (bbox.Max.U - bbox.Min.U) * i / gridSize;
                        var v = bbox.Min.V + (bbox.Max.V - bbox.Min.V) * j / gridSize;

                        var uv = new UV(u, v);

                        if (face.IsInside(uv))
                        {
                            var point = face.Evaluate(uv);
                            points.Add(point);
                        }
                    }
                }

                // If no points found (shouldn't happen), add center point
                if (points.Count == 0)
                {
                    var centerUV = new UV(
                        (bbox.Min.U + bbox.Max.U) / 2,
                        (bbox.Min.V + bbox.Max.V) / 2);
                    points.Add(face.Evaluate(centerUV));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Face sampling failed: {ex.Message}");
                // Return empty list if sampling fails
            }

            return points;
        }

        private void OnProgressChanged(int current, int total, string operation, string elementId)
        {
            ProgressChanged?.Invoke(this, new DesignCheckProgressEventArgs
            {
                Current = current,
                Total = total,
                CurrentOperation = operation,
                ElementId = elementId
            });
        }

        private void OnStatusChanged(string status, bool isError, string checkType, Exception? exception = null)
        {
            StatusChanged?.Invoke(this, new DesignCheckStatusEventArgs
            {
                Status = status,
                IsError = isError,
                CheckType = checkType,
                Exception = exception
            });
        }

        #endregion
    }
}
