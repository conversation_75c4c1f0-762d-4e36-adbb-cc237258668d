using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using MEP.Pacifire.Services;
using Microsoft.Win32;
using MessageBox = System.Windows.MessageBox;
using SaveFileDialog = Microsoft.Win32.SaveFileDialog;
using Application = System.Windows.Forms.Application;
using System.Diagnostics;

namespace MEP.Pacifire.ViewModels
{
    /// <summary>
    /// Main ViewModel for the Fire Stopping Solution Exporter.
    /// Handles extraction, design checks, filtering, and export operations.
    /// </summary>
    public partial class MainViewModel : ObservableObject
    {
        private readonly Document _document;
        private readonly IExtractionService _extractionService;
        private readonly IDesignCheckService _designCheckService;
        private readonly IExcelExportService _excelExportService;
        private CancellationTokenSource? _cancellationTokenSource;

        #region Observable Properties

        /// <summary>
        /// Collection of fire stopping elements
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<FireStoppingElement> _fireStoppingElements = new();

        /// <summary>
        /// Filtered collection of fire stopping elements for display
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<FireStoppingElement> _filteredFireStoppingElements = new();

        /// <summary>
        /// Current filter settings
        /// </summary>
        [ObservableProperty]
        private FilterSettings _filterSettings = new();

        /// <summary>
        /// Available levels for filtering
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<LevelFilter> _availableLevels = new();

        /// <summary>
        /// Available categories for filtering
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<CategoryFilter> _availableCategories = new();

        /// <summary>
        /// Available linked models for filtering
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<LinkedModelFilter> _availableLinkedModels = new();

        /// <summary>
        /// Current status message
        /// </summary>
        [ObservableProperty]
        private string _statusMessage = "Ready";

        /// <summary>
        /// Current progress percentage
        /// </summary>
        [ObservableProperty]
        private double _progressPercentage = 0;

        /// <summary>
        /// Indicates if an operation is in progress
        /// </summary>
        [ObservableProperty]
        private bool _isOperationInProgress = false;

        /// <summary>
        /// Indicates if the sidebar is collapsed
        /// </summary>
        [ObservableProperty]
        private bool _isSidebarCollapsed = false;

        /// <summary>
        /// Selected fire stopping element for details view
        /// </summary>
        [ObservableProperty]
        private FireStoppingElement? _selectedElement;

        /// <summary>
        /// Total count of elements
        /// </summary>
        [ObservableProperty]
        private int _totalElementsCount = 0;

        /// <summary>
        /// Count of elements with failures
        /// </summary>
        [ObservableProperty]
        private int _failuresCount = 0;

        /// <summary>
        /// Export settings for Excel export
        /// </summary>
        [ObservableProperty]
        private ExportSettings _exportSettings;

        #endregion

        #region Commands
        [RelayCommand]
        private void RunPerformanceTest()
        {
            // This command is for performance testing purposes
            // It can be used to measure the time taken for various operations
            // For example, extracting elements, applying filters, etc.
            // Implementation can be added as needed for performance analysis
            var result = _designCheckService.AnalyzeAdjacencyPerformance(
                FireStoppingElements,
                FilterSettings.AdjacencyThreshold);
            MessageBox.Show($"Performance test completed. Check the output for results.\n\n{result}",
                "Performance Test", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// Command to extract and analyze fire stopping elements
        /// </summary>
        [RelayCommand]
        private void ExtractAndAnalyze()
        {
            if (IsOperationInProgress) return;

            if (!FilterSettings.SelectedLevels.Any(l => l.IsSelected))
            {
                MessageBox.Show("Please select at least one level", "No Level Selected", MessageBoxButton.OK, MessageBoxImage.Hand);
                return;
            }

            try
            {
                IsOperationInProgress = true;
                _cancellationTokenSource = new CancellationTokenSource();
                
                StatusMessage = "Starting extraction and analysis...";
                ProgressPercentage = 0;
                Application.DoEvents(); // Allow UI to update

                // Clear previous results
                FireStoppingElements.Clear();
                FilteredFireStoppingElements.Clear();

                // Validate document
                var validation = _extractionService.ValidateDocument(_document);
                if (!validation.IsValid)
                {
                    StatusMessage = $"Document validation failed: {validation.Summary}";
                    MessageBox.Show($"Document validation failed:\n\n{string.Join("\n", validation.Issues)}", 
                        "Validation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // Extract fire stopping elements
                StatusMessage = "Extracting fire stopping elements...";
                ProgressPercentage = 10;
                Application.DoEvents(); // Allow UI to update

                var fireStoppingElements = _extractionService.ExtractFireStoppingElements(
                    _document, FilterSettings, _cancellationTokenSource.Token);

                var fireStoppingList = fireStoppingElements.ToList();
                if (!fireStoppingList.Any())
                {
                    StatusMessage = "No fire stopping elements found with current filters.";
                    MessageBox.Show("No fire stopping elements found with the current filter settings.\n\nPlease check your linked model selections and try again.", 
                        "No Elements Found", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // Extract connected services
                StatusMessage = "Finding connected services...";
                ProgressPercentage = 30;
                Application.DoEvents(); // Allow UI to update

                var serviceElements = _extractionService.ExtractConnectedServices(
                    _document, fireStoppingList, FilterSettings, _cancellationTokenSource.Token);

                // Extract structural elements
                StatusMessage = "Extracting structural elements...";
                ProgressPercentage = 50;
                Application.DoEvents(); // Allow UI to update

                var structuralElements = _extractionService.ExtractStructuralElements(
                    _document, FilterSettings, _cancellationTokenSource.Token);

                // Perform design checks
                StatusMessage = "Performing design checks...";
                ProgressPercentage = 70;
                Application.DoEvents(); // Allow UI to update

                var checkedElements = _designCheckService.PerformDesignChecks(
                    fireStoppingList, serviceElements, structuralElements,
                    FilterSettings, _cancellationTokenSource.Token);

                // Update UI
                StatusMessage = "Updating results...";
                ProgressPercentage = 90;
                Application.DoEvents(); // Allow UI to update

                // Update UI on main thread - since we're already in an async context,
                // the ObservableCollection updates should work directly
                foreach (var element in checkedElements)
                {
                    FireStoppingElements.Add(element);
                }

                ApplyFilters();
                UpdateCounts();

                ProgressPercentage = 100;
                Application.DoEvents(); // Allow UI to update
                StatusMessage = $"Analysis completed. Found {FireStoppingElements.Count} fire stopping elements.";
            }
            catch (OperationCanceledException)
            {
                StatusMessage = "Operation cancelled by user.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error during analysis: {ex.Message}";
                MessageBox.Show($"An error occurred during analysis:\n\n{ex.Message}", 
                    "Analysis Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsOperationInProgress = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// Command to cancel current operation
        /// </summary>
        [RelayCommand]
        private void CancelOperation()
        {
            _cancellationTokenSource?.Cancel();
            StatusMessage = "Cancelling operation...";
        }

        /// <summary>
        /// Command to apply filters
        /// </summary>
        [RelayCommand]
        private void ApplyFilters()
        {
            try
            {
                // Check if we have any elements to filter
                if (FireStoppingElements == null || !FireStoppingElements.Any())
                {
                    FilteredFireStoppingElements.Clear();
                    UpdateCounts();
                    StatusMessage = "No elements to filter.";
                    return;
                }

                var filtered = FireStoppingElements.AsEnumerable();

                // Level filter
                var selectedLevels = FilterSettings.SelectedLevels?.Where(x => x.IsSelected).Select(x => x.Name).ToList();
                if (selectedLevels?.Any() == true)
                {
                    filtered = filtered.Where(e => !string.IsNullOrEmpty(e.LevelName) && selectedLevels.Contains(e.LevelName));
                }

                // Category filter
                var selectedCategories = FilterSettings.SelectedCategories?.Where(x => x.IsSelected).Select(x => x.Name).ToList();
                if (selectedCategories?.Any() == true)
                {
                    filtered = filtered.Where(e => !string.IsNullOrEmpty(e.Category) && selectedCategories.Contains(e.Category));
                }

                // Linked Models filter
                var selectedLinkedModels = FilterSettings.SelectedLinkedModels?.Where(x => x.IsSelected).Select(x => x.Name).ToList();
                if (selectedLinkedModels?.Any() == true)
                {
                    filtered = filtered.Where(e => !string.IsNullOrEmpty(e.LinkedModelName) && selectedLinkedModels.Contains(e.LinkedModelName));
                }

                // Failures only filter
                if (FilterSettings.ShowFailuresOnly)
                {
                    filtered = filtered.Where(e => e.HasFailures);
                }

                // Search text filter
                if (!string.IsNullOrEmpty(FilterSettings.SearchText))
                {
                    var searchText = FilterSettings.SearchText.ToLowerInvariant();
                    filtered = filtered.Where(e =>
                        (!string.IsNullOrEmpty(e.DisplayName) && e.DisplayName.ToLowerInvariant().Contains(searchText)) ||
                        (!string.IsNullOrEmpty(e.BecaTypeMark) && e.BecaTypeMark.ToLowerInvariant().Contains(searchText)) ||
                        (!string.IsNullOrEmpty(e.BecaInstMark) && e.BecaInstMark.ToLowerInvariant().Contains(searchText)));
                }

                // Apply the filtered results
                FilteredFireStoppingElements.Clear();
                var filteredList = filtered.ToList(); // Materialize the query

                foreach (var element in filteredList)
                {
                    FilteredFireStoppingElements.Add(element);
                }

                UpdateCounts();
                StatusMessage = $"Showing {FilteredFireStoppingElements.Count} of {FireStoppingElements.Count} elements.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error applying filters: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"Filter error: {ex}");
            }
        }

        /// <summary>
        /// Command to clear all filters
        /// </summary>
        [RelayCommand]
        private void ClearFilters()
        {
            FilterSettings.Reset();
            ApplyFilters();
        }

        /// <summary>
        /// Command to select all levels
        /// </summary>
        [RelayCommand]
        private void SelectAllLevels()
        {
            if (FilterSettings.SelectedLevels != null)
            {
                foreach (var level in FilterSettings.SelectedLevels)
                {
                    level.IsSelected = true;
                }
            }
        }

        /// <summary>
        /// Command to deselect all levels
        /// </summary>
        [RelayCommand]
        private void DeselectAllLevels()
        {
            if (FilterSettings.SelectedLevels != null)
            {
                foreach (var level in FilterSettings.SelectedLevels)
                {
                    level.IsSelected = false;
                }
            }
        }

        /// <summary>
        /// Command to select all categories
        /// </summary>
        [RelayCommand]
        private void SelectAllCategories()
        {
            if (FilterSettings.SelectedCategories != null)
            {
                foreach (var category in FilterSettings.SelectedCategories)
                {
                    category.IsSelected = true;
                }
            }
        }

        /// <summary>
        /// Command to deselect all categories
        /// </summary>
        [RelayCommand]
        private void DeselectAllCategories()
        {
            if (FilterSettings.SelectedCategories != null)
            {
                foreach (var category in FilterSettings.SelectedCategories)
                {
                    category.IsSelected = false;
                }
            }
        }

        /// <summary>
        /// Command to select all linked models
        /// </summary>
        [RelayCommand]
        private void SelectAllLinkedModels()
        {
            if (FilterSettings.SelectedLinkedModels != null)
            {
                foreach (var model in FilterSettings.SelectedLinkedModels)
                {
                    model.IsSelected = true;
                }
            }
        }

        /// <summary>
        /// Command to deselect all linked models
        /// </summary>
        [RelayCommand]
        private void DeselectAllLinkedModels()
        {
            if (FilterSettings.SelectedLinkedModels != null)
            {
                foreach (var model in FilterSettings.SelectedLinkedModels)
                {
                    model.IsSelected = false;
                }
            }
        }

        /// <summary>
        /// Command to toggle sidebar collapse state
        /// </summary>
        [RelayCommand]
        private void ToggleSidebar()
        {
            IsSidebarCollapsed = !IsSidebarCollapsed;
        }

        /// <summary>
        /// Command to export to Excel
        /// </summary>
        [RelayCommand]
        private void ExportToExcel()
        {
            if (IsOperationInProgress || !FilteredFireStoppingElements.Any()) return;

            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files (*.xlsx)|*.xlsx|All Files (*.*)|*.*",
                    DefaultExt = "xlsx",
                    FileName = $"FireStoppingAnalysis_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    IsOperationInProgress = true;
                    _cancellationTokenSource = new CancellationTokenSource();
                    
                    StatusMessage = "Exporting to Excel...";
                    ProgressPercentage = 0;
                    Application.DoEvents(); // Allow UI to update

                    var result = _excelExportService.ExportToExcel(
                        FilteredFireStoppingElements, FilterSettings, saveFileDialog.FileName,
                        ExportSettings, _cancellationTokenSource.Token);

                    if (result.Success)
                    {
                        StatusMessage = $"Export completed successfully. File saved: {result.FilePath}";
                        MessageBox.Show($"Export completed successfully!\n\nFile: {result.FilePath}\nElements exported: {result.ElementsExported}\nFile size: {result.FileSizeBytes / 1024:F0} KB", 
                            "Export Successful", MessageBoxButton.OK, MessageBoxImage.Information);

                        // Open the excel file
                        Process.Start(new ProcessStartInfo
                        {
                            FileName = result.FilePath,
                            UseShellExecute = true // Required for opening files 
                        });

                    }
                    else
                    {
                        StatusMessage = $"Export failed: {result.ErrorMessage}";
                        MessageBox.Show($"Export failed:\n\n{result.ErrorMessage}", 
                            "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error during export: {ex.Message}";
                MessageBox.Show($"An error occurred during export:\n\n{ex.Message}", 
                    "Export Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsOperationInProgress = false;
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// Command to refresh available filters
        /// </summary>
        [RelayCommand]
        private void RefreshFilters()
        {
            try
            {
                StatusMessage = "Refreshing filters...";

                // Get available levels
                var levels = _extractionService.GetAvailableLevels(_document);
                AvailableLevels.Clear();
                foreach (var level in levels)
                {
                    AvailableLevels.Add(level);
                }

                // Get available categories
                var categories = _extractionService.GetAvailableCategories(_document);
                AvailableCategories.Clear();
                foreach (var category in categories)
                {
                    AvailableCategories.Add(category);
                }

                // Get available linked models
                var linkedModels = _extractionService.GetAvailableLinkedModels(_document);
                AvailableLinkedModels.Clear();
                foreach (var model in linkedModels)
                {
                    AvailableLinkedModels.Add(model);
                }

                // Update filter settings collections
                FilterSettings.SelectedLevels.Clear();
                foreach (var level in AvailableLevels)
                {
                    level.IsSelected = false;
                    FilterSettings.SelectedLevels.Add(level);
                }

                FilterSettings.SelectedCategories.Clear();
                foreach (var category in AvailableCategories)
                {
                    FilterSettings.SelectedCategories.Add(category);
                }

                FilterSettings.SelectedLinkedModels.Clear();
                foreach (var model in AvailableLinkedModels)
                {
                    FilterSettings.SelectedLinkedModels.Add(model);
                }

                StatusMessage = $"Filters refreshed. Found {AvailableLevels.Count} levels, {AvailableCategories.Count} categories, {AvailableLinkedModels.Count} linked models.";
            }
            catch (Exception ex)
            {
                StatusMessage = $"Error refreshing filters: {ex.Message}";
            }
        }

        #endregion

        #region Constructor

        public MainViewModel(
            Document document,
            IExtractionService extractionService,
            IDesignCheckService designCheckService,
            IExcelExportService excelExportService)
        {
            _document = document ?? throw new ArgumentNullException(nameof(document));
            _extractionService = extractionService ?? throw new ArgumentNullException(nameof(extractionService));
            _designCheckService = designCheckService ?? throw new ArgumentNullException(nameof(designCheckService));
            _excelExportService = excelExportService ?? throw new ArgumentNullException(nameof(excelExportService));

            // Initialize export settings
            ExportSettings = _excelExportService.GetDefaultExportSettings();
            ExportSettings.ProjectName = _document.Title;

            // Subscribe to service events
            SubscribeToServiceEvents();

            // Initialize filters
            RefreshFilters();
        }

        #endregion

        #region Private Methods

        private void SubscribeToServiceEvents()
        {
            _extractionService.ProgressChanged += OnExtractionProgressChanged;
            _extractionService.StatusChanged += OnExtractionStatusChanged;
            _designCheckService.ProgressChanged += OnDesignCheckProgressChanged;
            _designCheckService.StatusChanged += OnDesignCheckStatusChanged;
            _excelExportService.ProgressChanged += OnExportProgressChanged;
            _excelExportService.StatusChanged += OnExportStatusChanged;
        }

        private void OnExtractionProgressChanged(object? sender, ExtractionProgressEventArgs e)
        {
            // Update properties directly - ObservableObject handles property change notifications
            ProgressPercentage = Math.Min(30, e.PercentComplete * 0.3); // Extraction is 30% of total
            StatusMessage = e.CurrentOperation;
        }

        private void OnExtractionStatusChanged(object? sender, ExtractionStatusEventArgs e)
        {
            if (!e.IsError)
            {
                StatusMessage = e.Status;
            }
        }

        private void OnDesignCheckProgressChanged(object? sender, DesignCheckProgressEventArgs e)
        {
            ProgressPercentage = 50 + (e.PercentComplete * 0.4); // Design checks are 40% of total (50-90%)
            StatusMessage = e.CurrentOperation;
        }

        private void OnDesignCheckStatusChanged(object? sender, DesignCheckStatusEventArgs e)
        {
            if (!e.IsError)
            {
                StatusMessage = e.Status;
            }
        }

        private void OnExportProgressChanged(object? sender, ExportProgressEventArgs e)
        {
            ProgressPercentage = e.PercentComplete;
            StatusMessage = e.CurrentOperation;
        }

        private void OnExportStatusChanged(object? sender, ExportStatusEventArgs e)
        {
            if (!e.IsError)
            {
                StatusMessage = e.Status;
            }
        }

        private void UpdateCounts()
        {
            TotalElementsCount = FilteredFireStoppingElements.Count;
            FailuresCount = FilteredFireStoppingElements.Count(e => e.HasFailures);
        }

        #endregion
    }
}
