using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Interface for geometry operations and coordinate transformations.
    /// Critical for accurate spatial analysis across linked models.
    /// </summary>
    public interface IGeometryHelper
    {
        /// <summary>
        /// Transforms element geometry from linked model coordinates to host model coordinates
        /// </summary>
        /// <param name="element">Source element</param>
        /// <param name="linkTransform">Transform from link instance</param>
        /// <param name="targetElement">Target element to update with transformed geometry</param>
        void TransformElementGeometry(Element element, Transform linkTransform, FireStoppingElement targetElement);

        /// <summary>
        /// Transforms element geometry from linked model coordinates to host model coordinates
        /// </summary>
        /// <param name="element">Source element</param>
        /// <param name="linkTransform">Transform from link instance</param>
        /// <param name="targetElement">Target element to update with transformed geometry</param>
        void TransformElementGeometry(Element element, Transform linkTransform, ServiceElement targetElement);

        /// <summary>
        /// Transforms element geometry from linked model coordinates to host model coordinates
        /// </summary>
        /// <param name="element">Source element</param>
        /// <param name="linkTransform">Transform from link instance</param>
        /// <param name="targetElement">Target element to update with transformed geometry</param>
        void TransformElementGeometry(Element element, Transform linkTransform, StructuralElement targetElement);

        /// <summary>
        /// Extracts solid geometry from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Solid geometry or null if not available</returns>
        Solid? ExtractSolid(Element element);

        /// <summary>
        /// Transforms a solid using the specified transform
        /// </summary>
        /// <param name="solid">Original solid</param>
        /// <param name="transform">Transformation to apply</param>
        /// <returns>Transformed solid</returns>
        Solid? TransformSolid(Solid solid, Transform transform);

        /// <summary>
        /// Gets the location point of an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Location point or XYZ.Zero if not available</returns>
        XYZ GetElementLocation(Element element);

        /// <summary>
        /// Transforms a point using the specified transform
        /// </summary>
        /// <param name="point">Original point</param>
        /// <param name="transform">Transformation to apply</param>
        /// <returns>Transformed point</returns>
        XYZ TransformPoint(XYZ point, Transform transform);

        /// <summary>
        /// Gets the bounding box of an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Bounding box or null if not available</returns>
        BoundingBoxXYZ? GetElementBoundingBox(Element element);

        /// <summary>
        /// Transforms a bounding box using the specified transform
        /// </summary>
        /// <param name="boundingBox">Original bounding box</param>
        /// <param name="transform">Transformation to apply</param>
        /// <returns>Transformed bounding box</returns>
        BoundingBoxXYZ? TransformBoundingBox(BoundingBoxXYZ boundingBox, Transform transform);

        /// <summary>
        /// Checks if two solids intersect
        /// </summary>
        /// <param name="solid1">First solid</param>
        /// <param name="solid2">Second solid</param>
        /// <returns>True if solids intersect</returns>
        bool DoSolidsIntersect(Solid solid1, Solid solid2);

        /// <summary>
        /// Calculates the intersection volume between two solids
        /// </summary>
        /// <param name="solid1">First solid</param>
        /// <param name="solid2">Second solid</param>
        /// <returns>Intersection volume in cubic feet</returns>
        double CalculateIntersectionVolume(Solid solid1, Solid solid2);

        /// <summary>
        /// Checks if two bounding boxes intersect (for performance optimization)
        /// </summary>
        /// <param name="box1">First bounding box</param>
        /// <param name="box2">Second bounding box</param>
        /// <returns>True if bounding boxes intersect</returns>
        bool DoBoundingBoxesIntersect(BoundingBoxXYZ box1, BoundingBoxXYZ box2);

        /// <summary>
        /// Calculates the distance between two points
        /// </summary>
        /// <param name="point1">First point</param>
        /// <param name="point2">Second point</param>
        /// <returns>Distance in feet</returns>
        double CalculateDistance(XYZ point1, XYZ point2);

        /// <summary>
        /// Calculates the distance between two bounding boxes
        /// </summary>
        /// <param name="box1">First bounding box</param>
        /// <param name="box2">Second bounding box</param>
        /// <returns>Distance in feet (0 if intersecting)</returns>
        double CalculateBoundingBoxDistance(BoundingBoxXYZ box1, BoundingBoxXYZ box2);

        /// <summary>
        /// Expands a bounding box by the specified distance
        /// </summary>
        /// <param name="boundingBox">Original bounding box</param>
        /// <param name="expansion">Expansion distance in feet</param>
        /// <returns>Expanded bounding box</returns>
        BoundingBoxXYZ ExpandBoundingBox(BoundingBoxXYZ boundingBox, double expansion);

        /// <summary>
        /// Converts distance from feet to millimeters
        /// </summary>
        /// <param name="feet">Distance in feet</param>
        /// <returns>Distance in millimeters</returns>
        double FeetToMillimeters(double feet);

        /// <summary>
        /// Converts distance from millimeters to feet
        /// </summary>
        /// <param name="millimeters">Distance in millimeters</param>
        /// <returns>Distance in feet</returns>
        double MillimetersToFeet(double millimeters);

        /// <summary>
        /// Gets the center point of a bounding box
        /// </summary>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Center point</returns>
        XYZ GetBoundingBoxCenter(BoundingBoxXYZ boundingBox);

        /// <summary>
        /// Gets the center point of a UV bounding box on a face
        /// </summary>
        /// <param name="boundingBox">UV bounding box</param>
        /// <param name="face">Face to evaluate UV coordinates on</param>
        /// <returns>Center point in XYZ coordinates</returns>
        XYZ GetBoundingBoxCenter(BoundingBoxUV boundingBox, Face face);

        /// <summary>
        /// Validates that a solid is suitable for intersection operations
        /// </summary>
        /// <param name="solid">Solid to validate</param>
        /// <returns>True if solid is valid for operations</returns>
        bool IsValidSolid(Solid solid);

        /// <summary>
        /// Creates a simplified bounding box solid for performance testing
        /// </summary>
        /// <param name="boundingBox">Bounding box</param>
        /// <returns>Solid representing the bounding box</returns>
        Solid? CreateBoundingBoxSolid(BoundingBoxXYZ boundingBox);

        /// <summary>
        /// Finds the closest point on a solid to a given point
        /// </summary>
        /// <param name="solid">Target solid</param>
        /// <param name="point">Reference point</param>
        /// <returns>Closest point on the solid</returns>
        XYZ? FindClosestPointOnSolid(Solid solid, XYZ point);

        /// <summary>
        /// Calculates the minimum distance between two solids
        /// </summary>
        /// <param name="solid1">First solid</param>
        /// <param name="solid2">Second solid</param>
        /// <returns>Minimum distance in feet</returns>
        double CalculateMinimumSolidDistance(Solid solid1, Solid solid2);
    }
}
