using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using ClosedXML.Excel;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Service for exporting fire stopping analysis results to Excel format using ClosedXML.
    /// Creates structured workbooks with grouped sections and metadata.
    /// </summary>
    public class ExcelExportService : IExcelExportService
    {
        public event EventHandler<ExportProgressEventArgs>? ProgressChanged;
        public event EventHandler<ExportStatusEventArgs>? StatusChanged;

        /// <summary>
        /// Exports fire stopping analysis results to Excel file
        /// </summary>
        public ExportResult ExportToExcel(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            FilterSettings filterSettings,
            string filePath,
            ExportSettings exportSettings,
            CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.Now;
            var result = new ExportResult { FilePath = filePath };

            try
            {
                OnStatusChanged("Starting Excel export...", false, "Initialize");

                // Validate inputs
                var validation = ValidateExportSettings(filePath, exportSettings);
                if (!validation.IsValid)
                {
                    result.ErrorMessage = string.Join("; ", validation.Issues);
                    return result;
                }

                var elementsList = fireStoppingElements.ToList();
                
                // Filter elements if needed
                if (exportSettings.FailuresOnly)
                {
                    elementsList = elementsList.Where(x => x.HasFailures).ToList();
                }

                OnStatusChanged($"Exporting {elementsList.Count} elements...", false, "Export");

                using var workbook = new XLWorkbook();
                var sheetsCreated = 0;

                // Create main data sheet
                OnProgressChanged(1, 5, "Creating main data sheet", "Main Data");
                Application.DoEvents(); // Allow UI to update
                CreateMainDataSheet(workbook, elementsList, exportSettings, cancellationToken);
                sheetsCreated++;

                // Create summary sheet
                if (exportSettings.IncludeSummary)
                {
                    OnProgressChanged(2, 5, "Creating summary sheet", "Summary");
                    Application.DoEvents(); // Allow UI to update
                    CreateSummarySheet(workbook, elementsList, filterSettings, cancellationToken);
                    sheetsCreated++;
                }

                // Create metadata sheet
                if (exportSettings.IncludeMetadata)
                {
                    OnProgressChanged(3, 5, "Creating metadata sheet", "Metadata");
                    Application.DoEvents(); // Allow UI to update
                    CreateMetadataSheet(workbook, filterSettings, exportSettings, cancellationToken);
                    sheetsCreated++;
                }

                // Create design checks sheet
                if (exportSettings.IncludeDesignChecks)
                {
                    OnProgressChanged(4, 5, "Creating design checks sheet", "Design Checks");
                    Application.DoEvents(); // Allow UI to update
                    CreateDesignChecksSheet(workbook, elementsList, cancellationToken);
                    sheetsCreated++;
                }

                // Save the workbook
                OnProgressChanged(5, 5, "Saving workbook", "Save");
                OnStatusChanged("Saving Excel file...", false, "Save");
                Application.DoEvents(); // Allow UI to update

                workbook.SaveAs(filePath);

                // Get file info
                var fileInfo = new FileInfo(filePath);
                
                result.Success = true;
                result.ElementsExported = elementsList.Count;
                result.SheetsCreated = sheetsCreated;
                result.FileSizeBytes = fileInfo.Length;
                result.ExportDuration = DateTime.Now - startTime;

                OnStatusChanged($"Export completed successfully. File saved: {filePath}", false, "Complete");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Exception = ex;
                result.ExportDuration = DateTime.Now - startTime;

                OnStatusChanged($"Export failed: {ex.Message}", true, "Error", ex);
            }

            return result;
        }

        /// <summary>
        /// Creates a preview of the Excel export structure
        /// </summary>
        public ExportPreview CreateExportPreview(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            ExportSettings exportSettings)
        {
            var elementsList = fireStoppingElements.ToList();
            
            if (exportSettings.FailuresOnly)
            {
                elementsList = elementsList.Where(x => x.HasFailures).ToList();
            }

            var preview = new ExportPreview
            {
                TotalElements = elementsList.Count,
                ElementsWithFailures = elementsList.Count(x => x.HasFailures),
                EstimatedRows = elementsList.Count + 1, // +1 for header
                EstimatedColumns = CalculateColumnCount(exportSettings),
                EstimatedFileSizeKB = Math.Max(50, elementsList.Count / 10) // Rough estimate
            };

            // Determine sheets
            preview.SheetNames.Add("Main Data");
            if (exportSettings.IncludeSummary) preview.SheetNames.Add("Summary");
            if (exportSettings.IncludeMetadata) preview.SheetNames.Add("Metadata");
            if (exportSettings.IncludeDesignChecks) preview.SheetNames.Add("Design Checks");

            // Determine column groups
            if (exportSettings.IncludeFireStoppingDetails) preview.ColumnGroups.Add("Fire Stopping Info");
            if (exportSettings.IncludeServiceDetails) preview.ColumnGroups.Add("Service Info");
            if (exportSettings.IncludeStructureDetails) preview.ColumnGroups.Add("Structure Info");
            if (exportSettings.IncludeDesignChecks) preview.ColumnGroups.Add("Design Check Results");

            return preview;
        }

        /// <summary>
        /// Validates the export file path and settings
        /// </summary>
        public ValidationResult ValidateExportSettings(string filePath, ExportSettings exportSettings)
        {
            var result = new ValidationResult { IsValid = true };

            // Validate file path
            if (string.IsNullOrEmpty(filePath))
            {
                result.IsValid = false;
                result.Issues.Add("File path cannot be empty.");
            }
            else
            {
                try
                {
                    var directory = Path.GetDirectoryName(filePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        result.IsValid = false;
                        result.Issues.Add($"Directory does not exist: {directory}");
                    }

                    var extension = Path.GetExtension(filePath);
                    if (!extension.Equals(".xlsx", StringComparison.OrdinalIgnoreCase))
                    {
                        result.Warnings.Add("File extension should be .xlsx for best compatibility.");
                    }
                }
                catch (Exception ex)
                {
                    result.IsValid = false;
                    result.Issues.Add($"Invalid file path: {ex.Message}");
                }
            }

            // Validate export settings
            if (exportSettings == null)
            {
                result.IsValid = false;
                result.Issues.Add("Export settings cannot be null.");
            }
            else
            {
                if (!exportSettings.IncludeFireStoppingDetails && 
                    !exportSettings.IncludeServiceDetails && 
                    !exportSettings.IncludeStructureDetails && 
                    !exportSettings.IncludeDesignChecks)
                {
                    result.IsValid = false;
                    result.Issues.Add("At least one data section must be included in the export.");
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the default export settings
        /// </summary>
        public ExportSettings GetDefaultExportSettings()
        {
            return new ExportSettings
            {
                IncludeFireStoppingDetails = true,
                IncludeServiceDetails = true,
                IncludeStructureDetails = true,
                IncludeDesignChecks = true,
                FailuresOnly = false,
                IncludeMetadata = true,
                IncludeSummary = true,
                ApplyFormatting = true,
                FreezeHeaders = true,
                AutoFitColumns = true,
                AddFilters = true,
                ProjectName = "Fire Stopping Analysis",
                UserName = Environment.UserName
            };
        }

        #region Private Helper Methods

        private void CreateMainDataSheet(
            XLWorkbook workbook,
            List<FireStoppingElement> elements,
            ExportSettings settings,
            CancellationToken cancellationToken)
        {
                var worksheet = workbook.Worksheets.Add("Main Data");
                var currentColumn = 1;

                // Fire Stopping columns
                if (settings.IncludeFireStoppingDetails)
                {
                    currentColumn = AddFireStoppingColumns(worksheet, currentColumn);
                }

                // Service columns
                if (settings.IncludeServiceDetails)
                {
                    currentColumn = AddServiceColumns(worksheet, currentColumn);
                }

                // Structure columns
                if (settings.IncludeStructureDetails)
                {
                    currentColumn = AddStructureColumns(worksheet, currentColumn);
                }

                // Design check columns
                if (settings.IncludeDesignChecks)
                {
                    currentColumn = AddDesignCheckColumns(worksheet, currentColumn);
                }

                // Add data rows
                AddDataRows(worksheet, elements, settings);

                // Apply formatting
                if (settings.ApplyFormatting)
                {
                    ApplyWorksheetFormatting(worksheet, settings);
                }
        }

        private void CreateSummarySheet(
            XLWorkbook workbook,
            List<FireStoppingElement> elements,
            FilterSettings filterSettings,
            CancellationToken cancellationToken)
        {
            var worksheet = workbook.Worksheets.Add("Summary");

            // Summary statistics
            worksheet.Cell(1, 1).Value = "Fire Stopping Analysis Summary";
            worksheet.Cell(1, 1).Style.Font.Bold = true;
            worksheet.Cell(1, 1).Style.Font.FontSize = 16;

            var row = 3;
            worksheet.Cell(row++, 1).Value = "Total Elements:";
            worksheet.Cell(row - 1, 2).Value = elements.Count;

            worksheet.Cell(row++, 1).Value = "Elements with Failures:";
            worksheet.Cell(row - 1, 2).Value = elements.Count(x => x.HasFailures);

            worksheet.Cell(row++, 1).Value = "Elements Not Touching Wall:";
            worksheet.Cell(row - 1, 2).Value = elements.Count(x => x.DesignCheckResult.NotTouchingWall);

            worksheet.Cell(row++, 1).Value = "Elements Not Touching Service:";
            worksheet.Cell(row - 1, 2).Value = elements.Count(x => x.DesignCheckResult.NotTouchingService);

            worksheet.Cell(row++, 1).Value = "Clashing Elements:";
            worksheet.Cell(row - 1, 2).Value = elements.Count(x => x.DesignCheckResult.Clashing);

            worksheet.Cell(row++, 1).Value = "Adjacent Elements:";
            worksheet.Cell(row - 1, 2).Value = elements.Count(x => x.DesignCheckResult.Adjacent);
       
        }

        private void CreateMetadataSheet(
            XLWorkbook workbook,
            FilterSettings filterSettings,
            ExportSettings exportSettings,
            CancellationToken cancellationToken)
        {
            var worksheet = workbook.Worksheets.Add("Metadata");

            worksheet.Cell(1, 1).Value = "Export Metadata";
            worksheet.Cell(1, 1).Style.Font.Bold = true;
            worksheet.Cell(1, 1).Style.Font.FontSize = 16;

            var row = 3;
            worksheet.Cell(row++, 1).Value = "Project Name:";
            worksheet.Cell(row - 1, 2).Value = exportSettings.ProjectName;

            worksheet.Cell(row++, 1).Value = "Export Date:";
            worksheet.Cell(row - 1, 2).Value = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            worksheet.Cell(row++, 1).Value = "User:";
            worksheet.Cell(row - 1, 2).Value = exportSettings.UserName;

            worksheet.Cell(row++, 1).Value = "Filters Applied:";
            worksheet.Cell(row - 1, 2).Value = filterSettings.FilterSummary;

            worksheet.Cell(row++, 1).Value = "Adjacency Threshold:";
            worksheet.Cell(row - 1, 2).Value = $"{filterSettings.AdjacencyThreshold}mm";
    
        }

        private void CreateDesignChecksSheet(
            XLWorkbook workbook,
            List<FireStoppingElement> elements,
            CancellationToken cancellationToken)
        {
            var worksheet = workbook.Worksheets.Add("Design Checks");

            // Headers
            worksheet.Cell(1, 1).Value = "Element ID";
            worksheet.Cell(1, 2).Value = "Display Name";
            worksheet.Cell(1, 3).Value = "Not Touching Wall";
            worksheet.Cell(1, 4).Value = "Not Touching Service";
            worksheet.Cell(1, 5).Value = "Clashing";
            worksheet.Cell(1, 6).Value = "Adjacent";
            worksheet.Cell(1, 7).Value = "Total Failures";
            worksheet.Cell(1, 8).Value = "Check Summary";

            // Data
            for (int i = 0; i < elements.Count; i++)
            {
                var element = elements[i];
                var row = i + 2;

                worksheet.Cell(row, 1).Value = element.ElementId.ToString();
                worksheet.Cell(row, 2).Value = element.DisplayName;
                worksheet.Cell(row, 3).Value = element.DesignCheckResult.NotTouchingWall ? "FAIL" : "PASS";
                worksheet.Cell(row, 4).Value = element.DesignCheckResult.NotTouchingService ? "FAIL" : "PASS";
                worksheet.Cell(row, 5).Value = element.DesignCheckResult.Clashing ? "FAIL" : "PASS";
                worksheet.Cell(row, 6).Value = element.DesignCheckResult.Adjacent ? "FAIL" : "PASS";
                worksheet.Cell(row, 7).Value = element.DesignCheckResult.FailureCount;
                worksheet.Cell(row, 8).Value = element.DesignCheckResult.FailureSummary;
            }
        }

        private int AddFireStoppingColumns(IXLWorksheet worksheet, int startColumn)
        {
            var headers = new[]
            {
                "Element ID", "Family Name", "Type Name", "Beca Type Mark", "Beca Inst Mark",
                "Beca System Description", "Beca Family Material", "Beca Free Size",
                "Beca Family Orientation", "Beca Family Reference", "Level", "Category"
            };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cell(1, startColumn + i).Value = headers[i];
            }

            return startColumn + headers.Length;
        }

        private int AddServiceColumns(IXLWorksheet worksheet, int startColumn)
        {
            var headers = new[] { "Service Type", "Service Size", "Service Material", "Service System" };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cell(1, startColumn + i).Value = headers[i];
            }

            return startColumn + headers.Length;
        }

        private int AddStructureColumns(IXLWorksheet worksheet, int startColumn)
        {
            var headers = new[] { "Structure Type", "Structure Material", "Fire Rating", "Thickness" };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cell(1, startColumn + i).Value = headers[i];
            }

            return startColumn + headers.Length;
        }

        private int AddDesignCheckColumns(IXLWorksheet worksheet, int startColumn)
        {
            var headers = new[] { "Not Touching Wall", "Not Touching Service", "Clashing", "Adjacent", "Has Failures" };

            for (int i = 0; i < headers.Length; i++)
            {
                worksheet.Cell(1, startColumn + i).Value = headers[i];
            }

            return startColumn + headers.Length;
        }

        private void AddDataRows(IXLWorksheet worksheet, List<FireStoppingElement> elements, ExportSettings settings)
        {
            for (int i = 0; i < elements.Count; i++)
            {
                var element = elements[i];
                var row = i + 2;
                var column = 1;

                // Fire stopping data
                if (settings.IncludeFireStoppingDetails)
                {
                    worksheet.Cell(row, column++).Value = element.ElementId.ToString();
                    worksheet.Cell(row, column++).Value = element.FamilyName;
                    worksheet.Cell(row, column++).Value = element.TypeName;
                    worksheet.Cell(row, column++).Value = element.BecaTypeMark;
                    worksheet.Cell(row, column++).Value = element.BecaInstMark;
                    worksheet.Cell(row, column++).Value = element.BecaSystemDescription;
                    worksheet.Cell(row, column++).Value = element.BecaFamilyMaterial;
                    worksheet.Cell(row, column++).Value = element.BecaFreeSize;
                    worksheet.Cell(row, column++).Value = element.BecaFamilyOrientation;
                    worksheet.Cell(row, column++).Value = element.BecaFamilyReference;
                    worksheet.Cell(row, column++).Value = element.LevelName;
                    worksheet.Cell(row, column++).Value = element.Category;
                }

                // Service data
                if (settings.IncludeServiceDetails)
                {
                    var service = element.ConnectedService;
                    worksheet.Cell(row, column++).Value = service?.TypeName ?? "";
                    worksheet.Cell(row, column++).Value = service?.Size ?? "";
                    worksheet.Cell(row, column++).Value = service?.Material ?? "";
                    worksheet.Cell(row, column++).Value = service?.SystemType ?? "";
                }

                // Structure data
                if (settings.IncludeStructureDetails)
                {
                    var structure = element.AdjacentStructure;
                    worksheet.Cell(row, column++).Value = structure?.StructureType.ToString() ?? "";
                    worksheet.Cell(row, column++).Value = structure?.MaterialType ?? "";
                    worksheet.Cell(row, column++).Value = structure?.FireRating ?? "";
                    worksheet.Cell(row, column++).Value = structure?.FormattedThickness ?? "";
                }

                // Design check data
                if (settings.IncludeDesignChecks)
                {
                    var checks = element.DesignCheckResult;
                    worksheet.Cell(row, column++).Value = checks.NotTouchingWall ? "FAIL" : "PASS";
                    worksheet.Cell(row, column++).Value = checks.NotTouchingService ? "FAIL" : "PASS";
                    worksheet.Cell(row, column++).Value = checks.Clashing ? "FAIL" : "PASS";
                    worksheet.Cell(row, column++).Value = checks.Adjacent ? "FAIL" : "PASS";
                    worksheet.Cell(row, column++).Value = checks.HasFailures ? "YES" : "NO";
                }
            }
        }

        private void ApplyWorksheetFormatting(IXLWorksheet worksheet, ExportSettings settings)
        {
            // Header formatting
            var headerRange = worksheet.Range(1, 1, 1, worksheet.LastColumnUsed().ColumnNumber());
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.BackgroundColor = XLColor.LightBlue;
            headerRange.Style.Border.OutsideBorder = XLBorderStyleValues.Thick;

            // Freeze headers
            if (settings.FreezeHeaders)
            {
                worksheet.SheetView.FreezeRows(1);
            }

            // Auto-fit columns
            if (settings.AutoFitColumns)
            {
                worksheet.Columns().AdjustToContents();
            }

            // Add filters
            if (settings.AddFilters)
            {
                var dataRange = worksheet.Range(1, 1, worksheet.LastRowUsed().RowNumber(), worksheet.LastColumnUsed().ColumnNumber());
                dataRange.SetAutoFilter();
            }
        }

        private int CalculateColumnCount(ExportSettings settings)
        {
            int count = 0;
            if (settings.IncludeFireStoppingDetails) count += 12;
            if (settings.IncludeServiceDetails) count += 4;
            if (settings.IncludeStructureDetails) count += 4;
            if (settings.IncludeDesignChecks) count += 5;
            return count;
        }

        private void OnProgressChanged(int current, int total, string operation, string sheetName)
        {
            ProgressChanged?.Invoke(this, new ExportProgressEventArgs
            {
                Current = current,
                Total = total,
                CurrentOperation = operation,
                SheetName = sheetName
            });
        }

        private void OnStatusChanged(string status, bool isError, string operation, Exception? exception = null)
        {
            StatusChanged?.Invoke(this, new ExportStatusEventArgs
            {
                Status = status,
                IsError = isError,
                Operation = operation,
                Exception = exception
            });
        }

        #endregion
    }
}
