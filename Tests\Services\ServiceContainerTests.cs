using System;
using Xunit;
using FluentAssertions;
using Moq;
using Microsoft.Extensions.DependencyInjection;
using Autodesk.Revit.DB;
using MEP.Pacifire.Services;
using MEP.Pacifire.Helpers;
using MEP.Pacifire.ViewModels;

namespace MEP.Pacifire.Tests.Services
{
    /// <summary>
    /// Unit tests for ServiceContainer dependency injection configuration.
    /// Tests service registration, resolution, and validation.
    /// </summary>
    public class ServiceContainerTests : IDisposable
    {
        private readonly Mock<Document> _mockDocument;

        public ServiceContainerTests()
        {
            _mockDocument = new Mock<Document>();
            
            // Clean up any existing service container
            ServiceContainer.Dispose();
        }

        public void Dispose()
        {
            ServiceContainer.Dispose();
        }

        [Fact]
        public void ConfigureServices_WithValidDocument_ConfiguresContainer()
        {
            // Act
            var serviceProvider = ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Assert
            serviceProvider.Should().NotBeNull();
            ServiceContainer.IsConfigured.Should().BeTrue();
        }

        [Fact]
        public void ConfigureServices_WithNullDocument_ThrowsException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => ServiceContainer.ConfigureServices(null!));
        }

        [Fact]
        public void GetService_WithRegisteredService_ReturnsService()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var geometryHelper = ServiceContainer.GetService<IGeometryHelper>();

            // Assert
            geometryHelper.Should().NotBeNull();
            geometryHelper.Should().BeOfType<GeometryHelper>();
        }

        [Fact]
        public void GetService_WithUnregisteredService_ThrowsException()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => ServiceContainer.GetService<IDisposable>());
        }

        [Fact]
        public void GetService_WithoutConfiguration_ThrowsException()
        {
            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => ServiceContainer.GetService<IGeometryHelper>());
        }

        [Fact]
        public void GetRequiredService_WithRegisteredService_ReturnsService()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var spatialHelper = ServiceContainer.GetRequiredService<ISpatialHelper>();

            // Assert
            spatialHelper.Should().NotBeNull();
            spatialHelper.Should().BeOfType<SpatialHelper>();
        }

        [Fact]
        public void GetRequiredService_WithUnregisteredService_ThrowsException()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() => ServiceContainer.GetRequiredService<IDisposable>());
        }

        [Fact]
        public void GetServiceOrDefault_WithRegisteredService_ReturnsService()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);
            var fallback = new Mock<IParameterHelper>().Object;

            // Act
            var parameterHelper = ServiceContainer.GetServiceOrDefault(fallback);

            // Assert
            parameterHelper.Should().NotBeNull();
            parameterHelper.Should().NotBe(fallback);
            parameterHelper.Should().BeOfType<ParameterHelper>();
        }

        [Fact]
        public void GetServiceOrDefault_WithUnregisteredService_ReturnsFallback()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);
            var fallback = new Mock<IDisposable>().Object;

            // Act
            var service = ServiceContainer.GetServiceOrDefault(fallback);

            // Assert
            service.Should().Be(fallback);
        }

        [Fact]
        public void ValidateServices_WithProperConfiguration_ReturnsTrue()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var isValid = ServiceContainer.ValidateServices();

            // Assert
            isValid.Should().BeTrue();
        }

        [Fact]
        public void ValidateServices_WithoutConfiguration_ReturnsFalse()
        {
            // Act
            var isValid = ServiceContainer.ValidateServices();

            // Assert
            isValid.Should().BeFalse();
        }

        [Fact]
        public void GetValidationDetails_WithProperConfiguration_ReturnsValidResult()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var validation = ServiceContainer.GetValidationDetails();

            // Assert
            validation.Should().NotBeNull();
            validation.IsValid.Should().BeTrue();
            validation.Issues.Should().BeEmpty();
            validation.RegisteredServices.Should().NotBeEmpty();
            validation.RegisteredServices.Should().Contain("IGeometryHelper");
            validation.RegisteredServices.Should().Contain("ISpatialHelper");
            validation.RegisteredServices.Should().Contain("IParameterHelper");
            validation.RegisteredServices.Should().Contain("IExtractionService");
            validation.RegisteredServices.Should().Contain("IDesignCheckService");
            validation.RegisteredServices.Should().Contain("IExcelExportService");
            validation.RegisteredServices.Should().Contain("MainViewModel");
            validation.RegisteredServices.Should().Contain("Document");
        }

        [Fact]
        public void GetValidationDetails_WithoutConfiguration_ReturnsInvalidResult()
        {
            // Act
            var validation = ServiceContainer.GetValidationDetails();

            // Assert
            validation.Should().NotBeNull();
            validation.IsValid.Should().BeFalse();
            validation.Issues.Should().NotBeEmpty();
            validation.Issues.Should().Contain("Service provider is not configured");
        }

        [Fact]
        public void ServiceProvider_Property_ReturnsConfiguredProvider()
        {
            // Arrange
            var configuredProvider = ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var provider = ServiceContainer.ServiceProvider;

            // Assert
            provider.Should().Be(configuredProvider);
        }

        [Fact]
        public void ServiceProvider_Property_WithoutConfiguration_ReturnsNull()
        {
            // Act
            var provider = ServiceContainer.ServiceProvider;

            // Assert
            provider.Should().BeNull();
        }

        [Fact]
        public void Dispose_CleansUpResources()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);
            ServiceContainer.IsConfigured.Should().BeTrue();

            // Act
            ServiceContainer.Dispose();

            // Assert
            ServiceContainer.IsConfigured.Should().BeFalse();
            ServiceContainer.ServiceProvider.Should().BeNull();
        }

        [Fact]
        public void ConfigureServices_CalledTwice_ReplacesConfiguration()
        {
            // Arrange
            var firstProvider = ServiceContainer.ConfigureServices(_mockDocument.Object);
            
            // Act
            var secondProvider = ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Assert
            secondProvider.Should().NotBe(firstProvider);
            ServiceContainer.ServiceProvider.Should().Be(secondProvider);
        }

        [Fact]
        public void AllCoreServices_AreRegisteredAsSingleton()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var geometryHelper1 = ServiceContainer.GetService<IGeometryHelper>();
            var geometryHelper2 = ServiceContainer.GetService<IGeometryHelper>();
            var spatialHelper1 = ServiceContainer.GetService<ISpatialHelper>();
            var spatialHelper2 = ServiceContainer.GetService<ISpatialHelper>();

            // Assert
            geometryHelper1.Should().BeSameAs(geometryHelper2);
            spatialHelper1.Should().BeSameAs(spatialHelper2);
        }

        [Fact]
        public void BusinessServices_AreRegisteredAsScoped()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);
            var serviceProvider = ServiceContainer.ServiceProvider!;

            // Act
            using var scope1 = serviceProvider.CreateScope();
            using var scope2 = serviceProvider.CreateScope();
            
            var extractionService1 = scope1.ServiceProvider.GetService<IExtractionService>();
            var extractionService2 = scope1.ServiceProvider.GetService<IExtractionService>();
            var extractionService3 = scope2.ServiceProvider.GetService<IExtractionService>();

            // Assert
            extractionService1.Should().BeSameAs(extractionService2); // Same within scope
            extractionService1.Should().NotBeSameAs(extractionService3); // Different across scopes
        }

        [Fact]
        public void ViewModels_AreRegisteredAsTransient()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var viewModel1 = ServiceContainer.GetService<MainViewModel>();
            var viewModel2 = ServiceContainer.GetService<MainViewModel>();

            // Assert
            viewModel1.Should().NotBeSameAs(viewModel2);
        }

        [Fact]
        public void Document_IsRegisteredAsSingleton()
        {
            // Arrange
            ServiceContainer.ConfigureServices(_mockDocument.Object);

            // Act
            var document1 = ServiceContainer.GetService<Document>();
            var document2 = ServiceContainer.GetService<Document>();

            // Assert
            document1.Should().BeSameAs(document2);
            document1.Should().BeSameAs(_mockDocument.Object);
        }
    }
}
