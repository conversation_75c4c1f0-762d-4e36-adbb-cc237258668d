using System;
using System.Collections.Generic;
using Autodesk.Revit.DB;
using MEP.Pacifire.Models;
using PF_ServiceType = MEP.Pacifire.Models.PF_ServiceType;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Interface for extracting and managing Revit element parameters.
    /// Handles parameter extraction for fire stopping, service, and structural elements.
    /// </summary>
    public interface IParameterHelper
    {
        /// <summary>
        /// Extracts fire stopping specific parameters from a Revit element
        /// </summary>
        /// <param name="element">Source Revit element</param>
        /// <param name="fireStoppingElement">Target fire stopping element to populate</param>
        void ExtractFireStoppingParameters(Element element, FireStoppingElement fireStoppingElement);

        /// <summary>
        /// Extracts service specific parameters from a Revit element
        /// </summary>
        /// <param name="element">Source Revit element</param>
        /// <param name="serviceElement">Target service element to populate</param>
        void ExtractServiceParameters(Element element, ServiceElement serviceElement);

        /// <summary>
        /// Extracts structural specific parameters from a Revit element
        /// </summary>
        /// <param name="element">Source Revit element</param>
        /// <param name="structuralElement">Target structural element to populate</param>
        void ExtractStructuralParameters(Element element, StructuralElement structuralElement);

        /// <summary>
        /// Gets a parameter value as string from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value as string or empty string if not found</returns>
        string GetParameterValueAsString(Element element, string parameterName);

        /// <summary>
        /// Gets a parameter value as double from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value as double or 0 if not found</returns>
        double GetParameterValueAsDouble(Element element, string parameterName);

        /// <summary>
        /// Gets a parameter value as integer from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value as integer or 0 if not found</returns>
        int GetParameterValueAsInteger(Element element, string parameterName);

        /// <summary>
        /// Gets a parameter value as ElementId from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value as ElementId or InvalidElementId if not found</returns>
        ElementId GetParameterValueAsElementId(Element element, string parameterName);

        /// <summary>
        /// Checks if an element has a specific parameter
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>True if parameter exists</returns>
        bool HasParameter(Element element, string parameterName);

        /// <summary>
        /// Gets all parameter names and values from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Dictionary of parameter names and values</returns>
        Dictionary<string, object> GetAllParameters(Element element);

        /// <summary>
        /// Gets the family name from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Family name or empty string</returns>
        string GetFamilyName(Element element);

        /// <summary>
        /// Gets the type name from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Type name or empty string</returns>
        string GetTypeName(Element element);

        /// <summary>
        /// Gets the category name from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Category name or empty string</returns>
        string GetCategoryName(Element element);

        /// <summary>
        /// Gets the level name from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Level name or empty string</returns>
        string GetLevelName(Element element);

        /// <summary>
        /// Determines the service type from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Service type enumeration</returns>
        PF_ServiceType DetermineServiceType(Element element);

        /// <summary>
        /// Determines the structure type from an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Structure type enumeration</returns>
        StructureType DetermineStructureType(Element element);

        /// <summary>
        /// Gets the size parameter for a service element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <param name="serviceType">Type of service</param>
        /// <returns>Size string</returns>
        string GetServiceSize(Element element, PF_ServiceType serviceType);

        /// <summary>
        /// Gets the material parameter for an element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Material name</returns>
        string GetMaterialName(Element element);

        /// <summary>
        /// Gets the system type for a service element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>System type name</returns>
        string GetSystemType(Element element);

        /// <summary>
        /// Gets the fire rating for a structural element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Fire rating string</returns>
        string GetFireRating(Element element);

        /// <summary>
        /// Gets the thickness for a structural element
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Thickness in millimeters</returns>
        double GetThickness(Element element);

        /// <summary>
        /// Validates that required parameters are present for fire stopping analysis
        /// </summary>
        /// <param name="element">Revit element</param>
        /// <returns>Validation result</returns>
        ParameterValidationResult ValidateFireStoppingParameters(Element element);

        /// <summary>
        /// Gets custom parameter mappings for extensibility
        /// </summary>
        /// <returns>Dictionary of custom parameter mappings</returns>
        Dictionary<string, string> GetCustomParameterMappings();

        /// <summary>
        /// Sets custom parameter mappings for extensibility
        /// </summary>
        /// <param name="mappings">Custom parameter mappings</param>
        void SetCustomParameterMappings(Dictionary<string, string> mappings);
    }

    /// <summary>
    /// Result of parameter validation
    /// </summary>
    public class ParameterValidationResult
    {
        public bool IsValid { get; set; }
        public List<string> MissingParameters { get; set; } = new();
        public List<string> InvalidParameters { get; set; } = new();
        public List<string> Warnings { get; set; } = new();

        public bool HasIssues => MissingParameters.Count > 0 || InvalidParameters.Count > 0;
        public bool HasWarnings => Warnings.Count > 0;

        public string Summary
        {
            get
            {
                if (IsValid && !HasWarnings)
                    return "All required parameters are valid.";

                var summary = new List<string>();
                if (MissingParameters.Count > 0)
                    summary.Add($"{MissingParameters.Count} missing parameter(s)");
                if (InvalidParameters.Count > 0)
                    summary.Add($"{InvalidParameters.Count} invalid parameter(s)");
                if (HasWarnings)
                    summary.Add($"{Warnings.Count} warning(s)");

                return string.Join(", ", summary);
            }
        }
    }
}
