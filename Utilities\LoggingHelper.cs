using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.CompilerServices;
using MEP.Pacifire.Configuration;

namespace MEP.Pacifire.Utilities
{
    /// <summary>
    /// Logging helper for the fire stopping exporter.
    /// Provides centralized logging functionality with different levels.
    /// </summary>
    public static class LoggingHelper
    {
        private static readonly object _lock = new object();
        private static string _logFilePath;

        /// <summary>
        /// Gets the log file path
        /// </summary>
        private static string LogFilePath
        {
            get
            {
                if (string.IsNullOrEmpty(_logFilePath))
                {
                    var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                    var appFolder = Path.Combine(appDataPath, "MEP.Pacifire", "Logs");
                    Directory.CreateDirectory(appFolder);
                    _logFilePath = Path.Combine(appFolder, $"FireStoppingExporter_{DateTime.Now:yyyyMMdd}.log");
                }
                return _logFilePath;
            }
        }

        /// <summary>
        /// Logs an information message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="memberName">Calling member name (auto-filled)</param>
        /// <param name="sourceFilePath">Source file path (auto-filled)</param>
        /// <param name="sourceLineNumber">Source line number (auto-filled)</param>
        public static void LogInfo(string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            Log(LogLevel.Info, message, memberName, sourceFilePath, sourceLineNumber);
        }

        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="memberName">Calling member name (auto-filled)</param>
        /// <param name="sourceFilePath">Source file path (auto-filled)</param>
        /// <param name="sourceLineNumber">Source line number (auto-filled)</param>
        public static void LogWarning(string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            Log(LogLevel.Warning, message, memberName, sourceFilePath, sourceLineNumber);
        }

        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="memberName">Calling member name (auto-filled)</param>
        /// <param name="sourceFilePath">Source file path (auto-filled)</param>
        /// <param name="sourceLineNumber">Source line number (auto-filled)</param>
        public static void LogError(string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            Log(LogLevel.Error, message, memberName, sourceFilePath, sourceLineNumber);
        }

        /// <summary>
        /// Logs an exception
        /// </summary>
        /// <param name="exception">Exception to log</param>
        /// <param name="message">Additional message</param>
        /// <param name="memberName">Calling member name (auto-filled)</param>
        /// <param name="sourceFilePath">Source file path (auto-filled)</param>
        /// <param name="sourceLineNumber">Source line number (auto-filled)</param>
        public static void LogException(Exception exception, string message = "",
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            var fullMessage = string.IsNullOrEmpty(message) 
                ? exception.ToString() 
                : $"{message}: {exception}";
            
            Log(LogLevel.Error, fullMessage, memberName, sourceFilePath, sourceLineNumber);
        }

        /// <summary>
        /// Logs a debug message (only when debug logging is enabled)
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="memberName">Calling member name (auto-filled)</param>
        /// <param name="sourceFilePath">Source file path (auto-filled)</param>
        /// <param name="sourceLineNumber">Source line number (auto-filled)</param>
        public static void LogDebug(string message,
            [CallerMemberName] string memberName = "",
            [CallerFilePath] string sourceFilePath = "",
            [CallerLineNumber] int sourceLineNumber = 0)
        {
            if (AppSettings.Instance.EnableDebugLogging)
            {
                Log(LogLevel.Debug, message, memberName, sourceFilePath, sourceLineNumber);
            }
        }

        /// <summary>
        /// Core logging method
        /// </summary>
        /// <param name="level">Log level</param>
        /// <param name="message">Message to log</param>
        /// <param name="memberName">Calling member name</param>
        /// <param name="sourceFilePath">Source file path</param>
        /// <param name="sourceLineNumber">Source line number</param>
        private static void Log(LogLevel level, string message, string memberName, string sourceFilePath, int sourceLineNumber)
        {
            try
            {
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                var fileName = Path.GetFileName(sourceFilePath);
                var logEntry = $"[{timestamp}] [{level}] [{fileName}:{memberName}:{sourceLineNumber}] {message}";

                // Always write to debug output
                Debug.WriteLine(logEntry);

                // Write to file if enabled
                if (AppSettings.Instance.EnableDebugLogging || level != LogLevel.Debug)
                {
                    lock (_lock)
                    {
                        File.AppendAllText(LogFilePath, logEntry + Environment.NewLine);
                    }
                }
            }
            catch (Exception ex)
            {
                // Fallback to debug output if file logging fails
                Debug.WriteLine($"Logging error: {ex.Message}");
                Debug.WriteLine($"Original message: [{level}] {message}");
            }
        }

        /// <summary>
        /// Clears old log files (keeps last 7 days)
        /// </summary>
        public static void CleanupOldLogs()
        {
            try
            {
                var logDirectory = Path.GetDirectoryName(LogFilePath);
                if (!Directory.Exists(logDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-7);
                var logFiles = Directory.GetFiles(logDirectory, "FireStoppingExporter_*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(logFile);
                            LogInfo($"Deleted old log file: {Path.GetFileName(logFile)}");
                        }
                        catch (Exception ex)
                        {
                            LogWarning($"Failed to delete old log file {Path.GetFileName(logFile)}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogError($"Error during log cleanup: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current log file size in bytes
        /// </summary>
        /// <returns>Log file size or 0 if file doesn't exist</returns>
        public static long GetLogFileSize()
        {
            try
            {
                if (File.Exists(LogFilePath))
                {
                    var fileInfo = new FileInfo(LogFilePath);
                    return fileInfo.Length;
                }
            }
            catch (Exception ex)
            {
                LogError($"Error getting log file size: {ex.Message}");
            }
            return 0;
        }

        /// <summary>
        /// Opens the log file in the default text editor
        /// </summary>
        public static void OpenLogFile()
        {
            try
            {
                if (File.Exists(LogFilePath))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = LogFilePath,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                LogError($"Error opening log file: {ex.Message}");
            }
        }

        /// <summary>
        /// Opens the log directory in Windows Explorer
        /// </summary>
        public static void OpenLogDirectory()
        {
            try
            {
                var logDirectory = Path.GetDirectoryName(LogFilePath);
                if (Directory.Exists(logDirectory))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = logDirectory,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                LogError($"Error opening log directory: {ex.Message}");
            }
        }

        /// <summary>
        /// Logs application startup information
        /// </summary>
        public static void LogApplicationStart()
        {
            LogInfo("=== Fire Stopping Exporter Started ===");
            LogInfo($"Version: {System.Reflection.Assembly.GetExecutingAssembly().GetName().Version}");
            LogInfo($"OS: {Environment.OSVersion}");
            LogInfo($"CLR: {Environment.Version}");
            LogInfo($"User: {Environment.UserName}");
            LogInfo($"Machine: {Environment.MachineName}");
            LogInfo($"Working Directory: {Environment.CurrentDirectory}");
        }

        /// <summary>
        /// Logs application shutdown information
        /// </summary>
        public static void LogApplicationEnd()
        {
            LogInfo("=== Fire Stopping Exporter Ended ===");
        }

        /// <summary>
        /// Logs performance metrics
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="duration">Duration of the operation</param>
        /// <param name="itemCount">Number of items processed</param>
        public static void LogPerformance(string operationName, TimeSpan duration, int itemCount = 0)
        {
            var message = itemCount > 0 
                ? $"Performance: {operationName} completed in {duration.TotalMilliseconds:F2}ms ({itemCount} items, {duration.TotalMilliseconds / itemCount:F2}ms per item)"
                : $"Performance: {operationName} completed in {duration.TotalMilliseconds:F2}ms";
            
            LogInfo(message);
        }
    }

    /// <summary>
    /// Log levels for categorizing log messages
    /// </summary>
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error
    }
}
