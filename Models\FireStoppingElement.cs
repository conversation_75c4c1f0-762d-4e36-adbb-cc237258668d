using System;
using System.ComponentModel.DataAnnotations;
using CommunityToolkit.Mvvm.ComponentModel;
using Autodesk.Revit.DB;

namespace MEP.Pacifire.Models
{
    /// <summary>
    /// Represents a Fire Stopping fitting/accessory element extracted from MEP linked models.
    /// Contains all relevant parameters for fire stopping analysis and design checks.
    /// </summary>
    public partial class FireStoppingElement : ObservableObject
    {
        /// <summary>
        /// Unique identifier for the Revit element
        /// </summary>
        [ObservableProperty]
        private ElementId _elementId;

        /// <summary>
        /// Reference to the source Revit element
        /// </summary>
        [ObservableProperty]
        private Element _revitElement;

        /// <summary>
        /// Reference to the linked model instance containing this element
        /// </summary>
        [ObservableProperty]
        private RevitLinkInstance _linkInstance;

        /// <summary>
        /// Beca Type Mark parameter
        /// </summary>
        [ObservableProperty]
        private string _becaTypeMark = string.Empty;

        /// <summary>
        /// Beca Instance Mark parameter
        /// </summary>
        [ObservableProperty]
        private string _becaInstMark = string.Empty;

        /// <summary>
        /// Beca System Description parameter
        /// </summary>
        [ObservableProperty]
        private string _becaSystemDescription = string.Empty;

        /// <summary>
        /// Beca Family Material parameter
        /// </summary>
        [ObservableProperty]
        private string _becaFamilyMaterial = string.Empty;

        /// <summary>
        /// Beca Free Size parameter
        /// </summary>
        [ObservableProperty]
        private string _becaFreeSize = string.Empty;

        /// <summary>
        /// Beca Family Orientation parameter
        /// </summary>
        [ObservableProperty]
        private string _becaFamilyOrientation = string.Empty;

        /// <summary>
        /// Family Name
        /// </summary>
        [ObservableProperty]
        private string _familyName = string.Empty;

        /// <summary>
        /// Type Name
        /// </summary>
        [ObservableProperty]
        private string _typeName = string.Empty;

        /// <summary>
        /// Beca Family Reference parameter
        /// </summary>
        [ObservableProperty]
        private string _becaFamilyReference = string.Empty;

        /// <summary>
        /// Level name where the element is located
        /// </summary>
        [ObservableProperty]
        private string _levelName = string.Empty;

        /// <summary>
        /// Name of the linked model containing this element
        /// </summary>
        [ObservableProperty]
        private string _linkedModelName = string.Empty;

        /// <summary>
        /// Location point in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private XYZ _locationPoint;

        /// <summary>
        /// Bounding box in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private BoundingBoxXYZ _boundingBox;

        /// <summary>
        /// Solid geometry in host model coordinates (transformed)
        /// </summary>
        [ObservableProperty]
        private Solid _transformedSolid;

        /// <summary>
        /// Transform used to convert from linked model coordinates to host model coordinates.
        /// Used for debugging coordinate system issues with different Internal Origins.
        /// </summary>
        [ObservableProperty]
        private Transform _coordinateTransform;

        /// <summary>
        /// Name of the source linked model for coordinate system tracking.
        /// Used to identify potential coordinate system misalignments.
        /// </summary>
        [ObservableProperty]
        private string _sourceModelName = string.Empty;

        /// <summary>
        /// Connected service element (if any)
        /// </summary>
        [ObservableProperty]
        private ServiceElement? _connectedService;

        /// <summary>
        /// Adjacent structural element (if any)
        /// </summary>
        [ObservableProperty]
        private StructuralElement? _adjacentStructure;

        /// <summary>
        /// Design check results for this element
        /// </summary>
        [ObservableProperty]
        private DesignCheckResult _designCheckResult = new();

        /// <summary>
        /// Indicates if this element has any design check failures
        /// </summary>
        public bool HasFailures => DesignCheckResult.HasFailures;

        /// <summary>
        /// Category of the element (Duct Fittings, Pipe Fittings, etc.)
        /// </summary>
        [ObservableProperty]
        private string _category = string.Empty;

        /// <summary>
        /// Additional custom parameters that may be added in the future
        /// </summary>
        [ObservableProperty]
        private Dictionary<string, object> _customParameters = new();

        /// <summary>
        /// Timestamp when this element was extracted
        /// </summary>
        [ObservableProperty]
        private DateTime _extractedAt = DateTime.Now;

        /// <summary>
        /// Constructor for creating a new FireStoppingElement
        /// </summary>
        public FireStoppingElement()
        {
            ElementId = ElementId.InvalidElementId;
            LocationPoint = XYZ.Zero;
        }

        /// <summary>
        /// Constructor with basic element information
        /// </summary>
        /// <param name="elementId">Revit element ID</param>
        /// <param name="revitElement">Source Revit element</param>
        /// <param name="linkInstance">Source link instance</param>
        public FireStoppingElement(ElementId elementId, Element revitElement, RevitLinkInstance linkInstance)
        {
            ElementId = elementId;
            RevitElement = revitElement;
            LinkInstance = linkInstance;
            LocationPoint = XYZ.Zero;
        }

        /// <summary>
        /// Gets a display name for this fire stopping element
        /// </summary>
        public string DisplayName => !string.IsNullOrEmpty(BecaInstMark) 
            ? $"{BecaInstMark} ({FamilyName})" 
            : $"{FamilyName} - {ElementId}";

        /// <summary>
        /// Adds or updates a custom parameter
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <param name="value">Parameter value</param>
        public void SetCustomParameter(string parameterName, object value)
        {
            CustomParameters[parameterName] = value;
        }

        /// <summary>
        /// Gets a custom parameter value
        /// </summary>
        /// <param name="parameterName">Parameter name</param>
        /// <returns>Parameter value or null if not found</returns>
        public T? GetCustomParameter<T>(string parameterName)
        {
            if (CustomParameters.TryGetValue(parameterName, out var value) && value is T typedValue)
            {
                return typedValue;
            }
            return default(T);
        }

        /// <summary>
        /// Validates that all required parameters are present
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            return ElementId != ElementId.InvalidElementId &&
                   !string.IsNullOrEmpty(BecaTypeMark) &&
                   !string.IsNullOrEmpty(FamilyName) &&
                   RevitElement != null &&
                   LinkInstance != null;
        }

        /// <summary>
        /// Creates a copy of this element for comparison purposes
        /// </summary>
        /// <returns>A new FireStoppingElement with copied values</returns>
        public FireStoppingElement Clone()
        {
            return new FireStoppingElement(ElementId, RevitElement, LinkInstance)
            {
                BecaTypeMark = BecaTypeMark,
                BecaInstMark = BecaInstMark,
                BecaSystemDescription = BecaSystemDescription,
                BecaFamilyMaterial = BecaFamilyMaterial,
                BecaFreeSize = BecaFreeSize,
                BecaFamilyOrientation = BecaFamilyOrientation,
                FamilyName = FamilyName,
                TypeName = TypeName,
                BecaFamilyReference = BecaFamilyReference,
                LevelName = LevelName,
                LinkedModelName = LinkedModelName,
                LocationPoint = LocationPoint,
                BoundingBox = BoundingBox,
                TransformedSolid = TransformedSolid,
                Category = Category,
                CustomParameters = new Dictionary<string, object>(CustomParameters),
                ExtractedAt = ExtractedAt
            };
        }
    }
}
