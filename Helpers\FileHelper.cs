﻿using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// File helper class that provides async file operations compatible with .NET Framework.
    /// Wraps synchronous file operations in Task.Run for async compatibility.
    /// </summary>
    public static class FileHelper
    {
        /// <summary>
        /// Asynchronously writes text to a file, creating the file if it doesn't exist
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="contents">Text content to write</param>
        /// <returns>Task representing the async operation</returns>
        public static Task WriteAllTextAsync(string path, string contents)
        {
            return Task.Run(() => File.WriteAllText(path, contents));
        }

        /// <summary>
        /// Asynchronously writes text to a file with specified encoding
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="contents">Text content to write</param>
        /// <param name="encoding">Text encoding</param>
        /// <returns>Task representing the async operation</returns>
        public static Task WriteAllTextAsync(string path, string contents, Encoding encoding)
        {
            return Task.Run(() => File.WriteAllText(path, contents, encoding));
        }

        /// <summary>
        /// Asynchronously appends text to a file, creating the file if it doesn't exist
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="contents">Text content to append</param>
        /// <returns>Task representing the async operation</returns>
        public static Task AppendAllTextAsync(string path, string contents)
        {
            return Task.Run(() => File.AppendAllText(path, contents));
        }

        /// <summary>
        /// Asynchronously appends text to a file with specified encoding
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="contents">Text content to append</param>
        /// <param name="encoding">Text encoding</param>
        /// <returns>Task representing the async operation</returns>
        public static Task AppendAllTextAsync(string path, string contents, Encoding encoding)
        {
            return Task.Run(() => File.AppendAllText(path, contents, encoding));
        }

        /// <summary>
        /// Asynchronously reads all text from a file
        /// </summary>
        /// <param name="path">File path</param>
        /// <returns>Task containing the file contents as string</returns>
        public static Task<string> ReadAllTextAsync(string path)
        {
            return Task.Run(() => File.ReadAllText(path));
        }

        /// <summary>
        /// Asynchronously reads all text from a file with specified encoding
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="encoding">Text encoding</param>
        /// <returns>Task containing the file contents as string</returns>
        public static Task<string> ReadAllTextAsync(string path, Encoding encoding)
        {
            return Task.Run(() => File.ReadAllText(path, encoding));
        }

        /// <summary>
        /// Asynchronously reads all lines from a file
        /// </summary>
        /// <param name="path">File path</param>
        /// <returns>Task containing the file lines as string array</returns>
        public static Task<string[]> ReadAllLinesAsync(string path)
        {
            return Task.Run(() => File.ReadAllLines(path));
        }

        /// <summary>
        /// Asynchronously reads all lines from a file with specified encoding
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="encoding">Text encoding</param>
        /// <returns>Task containing the file lines as string array</returns>
        public static Task<string[]> ReadAllLinesAsync(string path, Encoding encoding)
        {
            return Task.Run(() => File.ReadAllLines(path, encoding));
        }

        /// <summary>
        /// Asynchronously writes all lines to a file
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="contents">Lines to write</param>
        /// <returns>Task representing the async operation</returns>
        public static Task WriteAllLinesAsync(string path, string[] contents)
        {
            return Task.Run(() => File.WriteAllLines(path, contents));
        }

        /// <summary>
        /// Asynchronously writes all lines to a file with specified encoding
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="contents">Lines to write</param>
        /// <param name="encoding">Text encoding</param>
        /// <returns>Task representing the async operation</returns>
        public static Task WriteAllLinesAsync(string path, string[] contents, Encoding encoding)
        {
            return Task.Run(() => File.WriteAllLines(path, contents, encoding));
        }

        /// <summary>
        /// Asynchronously reads all bytes from a file
        /// </summary>
        /// <param name="path">File path</param>
        /// <returns>Task containing the file bytes</returns>
        public static Task<byte[]> ReadAllBytesAsync(string path)
        {
            return Task.Run(() => File.ReadAllBytes(path));
        }

        /// <summary>
        /// Asynchronously writes all bytes to a file
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="bytes">Bytes to write</param>
        /// <returns>Task representing the async operation</returns>
        public static Task WriteAllBytesAsync(string path, byte[] bytes)
        {
            return Task.Run(() => File.WriteAllBytes(path, bytes));
        }

        /// <summary>
        /// Asynchronously checks if a file exists
        /// </summary>
        /// <param name="path">File path</param>
        /// <returns>Task containing true if file exists</returns>
        public static Task<bool> ExistsAsync(string path)
        {
            return Task.Run(() => File.Exists(path));
        }

        /// <summary>
        /// Asynchronously deletes a file
        /// </summary>
        /// <param name="path">File path</param>
        /// <returns>Task representing the async operation</returns>
        public static Task DeleteAsync(string path)
        {
            return Task.Run(() => File.Delete(path));
        }

        /// <summary>
        /// Asynchronously copies a file
        /// </summary>
        /// <param name="sourceFileName">Source file path</param>
        /// <param name="destFileName">Destination file path</param>
        /// <param name="overwrite">Whether to overwrite existing file</param>
        /// <returns>Task representing the async operation</returns>
        public static Task CopyAsync(string sourceFileName, string destFileName, bool overwrite = false)
        {
            return Task.Run(() => File.Copy(sourceFileName, destFileName, overwrite));
        }

        /// <summary>
        /// Asynchronously moves a file
        /// </summary>
        /// <param name="sourceFileName">Source file path</param>
        /// <param name="destFileName">Destination file path</param>
        /// <returns>Task representing the async operation</returns>
        public static Task MoveAsync(string sourceFileName, string destFileName)
        {
            return Task.Run(() => File.Move(sourceFileName, destFileName));
        }

        /// <summary>
        /// Asynchronously gets file information
        /// </summary>
        /// <param name="path">File path</param>
        /// <returns>Task containing FileInfo object</returns>
        public static Task<FileInfo> GetFileInfoAsync(string path)
        {
            return Task.Run(() => new FileInfo(path));
        }

        /// <summary>
        /// Safely writes text to a file with backup and rollback on failure
        /// </summary>
        /// <param name="path">File path</param>
        /// <param name="contents">Text content to write</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task SafeWriteAllTextAsync(string path, string contents)
        {
            var backupPath = path + ".backup";
            var tempPath = path + ".temp";

            try
            {
                // Create backup if original file exists
                if (File.Exists(path))
                {
                    await CopyAsync(path, backupPath, true);
                }

                // Write to temporary file first
                await WriteAllTextAsync(tempPath, contents);

                // Replace original with temporary file
                if (File.Exists(path))
                {
                    await DeleteAsync(path);
                }
                await MoveAsync(tempPath, path);

                // Clean up backup
                if (File.Exists(backupPath))
                {
                    await DeleteAsync(backupPath);
                }
            }
            catch
            {
                // Rollback on failure
                try
                {
                    if (File.Exists(tempPath))
                    {
                        await DeleteAsync(tempPath);
                    }

                    if (File.Exists(backupPath))
                    {
                        if (File.Exists(path))
                        {
                            await DeleteAsync(path);
                        }
                        await MoveAsync(backupPath, path);
                    }
                }
                catch
                {
                    // Ignore rollback errors
                }

                throw;
            }
        }
    }
}
