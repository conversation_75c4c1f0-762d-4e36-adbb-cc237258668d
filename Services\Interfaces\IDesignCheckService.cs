using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MEP.Pacifire.Helpers;
using MEP.Pacifire.Models;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Interface for performing design checks on fire stopping elements.
    /// Validates spatial relationships and identifies placement issues.
    /// </summary>
    public interface IDesignCheckService
    {
        /// <summary>
        /// Performs all design checks on fire stopping elements
        /// </summary>
        /// <param name="fireStoppingElements">Fire stopping elements to check</param>
        /// <param name="serviceElements">Available service elements</param>
        /// <param name="structuralElements">Available structural elements</param>
        /// <param name="filterSettings">Filter settings with check parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Updated fire stopping elements with check results</returns>
        IEnumerable<FireStoppingElement> PerformDesignChecks(
            IEnumerable<FireStoppingElement> fireStoppingElements,
            IEnumerable<ServiceElement> serviceElements,
            IEnumerable<StructuralElement> structuralElements,
            FilterSettings filterSettings,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Performs the "Not Touching Wall" check for a single element
        /// </summary>
        /// <param name="fireStoppingElement">Element to check</param>
        /// <param name="structuralElements">Available structural elements</param>
        /// <returns>Check result</returns>
        bool CheckNotTouchingWall(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements);

        /// <summary>
        /// Performs the "Not Touching Service" check for a single element
        /// </summary>
        /// <param name="fireStoppingElement">Element to check</param>
        /// <param name="serviceElements">Available service elements</param>
        /// <returns>Check result</returns>
        bool CheckNotTouchingService(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements);

        /// <summary>
        /// Performs the "Clashing" check for a single element
        /// </summary>
        /// <param name="fireStoppingElement">Element to check</param>
        /// <param name="otherFireStoppingElements">Other fire stopping elements</param>
        /// <returns>Check result and list of clashing elements</returns>
        (bool IsClashing, IEnumerable<ElementId> ClashingElements) CheckClashing(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements);

        /// <summary>
        /// Performs the "Adjacent" check for a single element
        /// </summary>
        /// <param name="fireStoppingElement">Element to check</param>
        /// <param name="otherFireStoppingElements">Other fire stopping elements</param>
        /// <param name="adjacencyThreshold">Distance threshold in millimeters</param>
        /// <returns>Check result and list of adjacent elements</returns>
        (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements) CheckAdjacent(
            FireStoppingElement fireStoppingElement,
            IEnumerable<FireStoppingElement> otherFireStoppingElements,
            double adjacencyThreshold = 300.0);

        /// <summary>
        /// Performs the "Adjacent" check for a single element using optimized spatial indexing
        /// </summary>
        /// <param name="fireStoppingElement">Element to check</param>
        /// <param name="spatialIndex">Spatial index for optimization</param>
        /// <param name="adjacencyThreshold">Distance threshold in millimeters</param>
        /// <returns>Check result and list of adjacent elements</returns>
        (bool IsAdjacent, IEnumerable<ElementId> AdjacentElements) CheckAdjacentOptimized(
            FireStoppingElement fireStoppingElement,
            SpatialIndex spatialIndex,
            double adjacencyThreshold = 300.0);

        /// <summary>
        /// Runs performance analysis comparing legacy and optimized adjacency check methods
        /// </summary>
        /// <param name="fireStoppingElements">Elements to analyze</param>
        /// <param name="adjacencyThreshold">Distance threshold in millimeters</param>
        /// <returns>Performance analysis report</returns>
        string AnalyzeAdjacencyPerformance(IEnumerable<FireStoppingElement> fireStoppingElements, double adjacencyThreshold = 300.0);

        /// <summary>
        /// Calculates the distance between two fire stopping elements
        /// </summary>
        /// <param name="element1">First element</param>
        /// <param name="element2">Second element</param>
        /// <returns>Distance in millimeters</returns>
        double CalculateDistance(FireStoppingElement element1, FireStoppingElement element2);

        /// <summary>
        /// Finds the nearest structural element to a fire stopping element
        /// </summary>
        /// <param name="fireStoppingElement">Fire stopping element</param>
        /// <param name="structuralElements">Available structural elements</param>
        /// <returns>Nearest structural element and distance</returns>
        (StructuralElement? NearestElement, double Distance) FindNearestStructuralElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<StructuralElement> structuralElements);

        /// <summary>
        /// Finds the nearest service element to a fire stopping element
        /// </summary>
        /// <param name="fireStoppingElement">Fire stopping element</param>
        /// <param name="serviceElements">Available service elements</param>
        /// <returns>Nearest service element and distance</returns>
        (ServiceElement? NearestElement, double Distance) FindNearestServiceElement(
            FireStoppingElement fireStoppingElement,
            IEnumerable<ServiceElement> serviceElements);

        /// <summary>
        /// Event raised when design check progress changes
        /// </summary>
        event EventHandler<DesignCheckProgressEventArgs> ProgressChanged;

        /// <summary>
        /// Event raised when design check status changes
        /// </summary>
        event EventHandler<DesignCheckStatusEventArgs> StatusChanged;
    }

    /// <summary>
    /// Event arguments for design check progress updates
    /// </summary>
    public class DesignCheckProgressEventArgs : EventArgs
    {
        public int Current { get; set; }
        public int Total { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public string ElementId { get; set; } = string.Empty;
        public double PercentComplete => Total > 0 ? (double)Current / Total * 100 : 0;
    }

    /// <summary>
    /// Event arguments for design check status updates
    /// </summary>
    public class DesignCheckStatusEventArgs : EventArgs
    {
        public string Status { get; set; } = string.Empty;
        public bool IsError { get; set; }
        public Exception? Exception { get; set; }
        public string CheckType { get; set; } = string.Empty;
    }
}
