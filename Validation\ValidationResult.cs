using System;
using System.Collections.Generic;
using System.Linq;

namespace MEP.Pacifire.Validation
{
    /// <summary>
    /// Represents the result of a validation operation.
    /// Contains validation status, issues, warnings, and recommendations.
    /// </summary>
    public class ValidationResult
    {
        /// <summary>
        /// Indicates if the validation passed without critical issues
        /// </summary>
        public bool IsValid { get; set; } = true;

        /// <summary>
        /// Critical issues that prevent operation from proceeding
        /// </summary>
        public List<string> Issues { get; set; } = new();

        /// <summary>
        /// Non-critical warnings that should be brought to user attention
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Recommendations for improving the operation or configuration
        /// </summary>
        public List<string> Recommendations { get; set; } = new();

        /// <summary>
        /// Additional context information
        /// </summary>
        public Dictionary<string, object> Context { get; set; } = new();

        /// <summary>
        /// Timestamp when validation was performed
        /// </summary>
        public DateTime ValidationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Indicates if there are any issues (critical or warnings)
        /// </summary>
        public bool HasIssues => Issues.Count > 0 || Warnings.Count > 0;

        /// <summary>
        /// Indicates if there are only warnings (no critical issues)
        /// </summary>
        public bool HasWarningsOnly => Issues.Count == 0 && Warnings.Count > 0;

        /// <summary>
        /// Total count of all issues and warnings
        /// </summary>
        public int TotalIssueCount => Issues.Count + Warnings.Count;

        /// <summary>
        /// Gets a summary of the validation result
        /// </summary>
        public string Summary
        {
            get
            {
                if (IsValid && !HasIssues)
                    return "Validation passed successfully.";

                var parts = new List<string>();

                if (!IsValid)
                    parts.Add($"{Issues.Count} critical issue(s)");

                if (Warnings.Count > 0)
                    parts.Add($"{Warnings.Count} warning(s)");

                if (Recommendations.Count > 0)
                    parts.Add($"{Recommendations.Count} recommendation(s)");

                return string.Join(", ", parts);
            }
        }

        /// <summary>
        /// Gets a detailed report of all validation results
        /// </summary>
        public string DetailedReport
        {
            get
            {
                var report = new List<string>
                {
                    $"Validation Report - {ValidationTime:yyyy-MM-dd HH:mm:ss}",
                    $"Status: {(IsValid ? "VALID" : "INVALID")}",
                    $"Summary: {Summary}",
                    ""
                };

                if (Issues.Count > 0)
                {
                    report.Add("CRITICAL ISSUES:");
                    for (int i = 0; i < Issues.Count; i++)
                    {
                        report.Add($"  {i + 1}. {Issues[i]}");
                    }
                    report.Add("");
                }

                if (Warnings.Count > 0)
                {
                    report.Add("WARNINGS:");
                    for (int i = 0; i < Warnings.Count; i++)
                    {
                        report.Add($"  {i + 1}. {Warnings[i]}");
                    }
                    report.Add("");
                }

                if (Recommendations.Count > 0)
                {
                    report.Add("RECOMMENDATIONS:");
                    for (int i = 0; i < Recommendations.Count; i++)
                    {
                        report.Add($"  {i + 1}. {Recommendations[i]}");
                    }
                    report.Add("");
                }

                if (Context.Count > 0)
                {
                    report.Add("ADDITIONAL CONTEXT:");
                    foreach (var kvp in Context)
                    {
                        report.Add($"  {kvp.Key}: {kvp.Value}");
                    }
                    report.Add("");
                }

                return string.Join(Environment.NewLine, report);
            }
        }

        /// <summary>
        /// Adds a critical issue to the validation result
        /// </summary>
        /// <param name="issue">Issue description</param>
        public void AddIssue(string issue)
        {
            if (!string.IsNullOrWhiteSpace(issue))
            {
                Issues.Add(issue);
                IsValid = false;
            }
        }

        /// <summary>
        /// Adds multiple critical issues to the validation result
        /// </summary>
        /// <param name="issues">Issue descriptions</param>
        public void AddIssues(IEnumerable<string> issues)
        {
            foreach (var issue in issues.Where(i => !string.IsNullOrWhiteSpace(i)))
            {
                AddIssue(issue);
            }
        }

        /// <summary>
        /// Adds a warning to the validation result
        /// </summary>
        /// <param name="warning">Warning description</param>
        public void AddWarning(string warning)
        {
            if (!string.IsNullOrWhiteSpace(warning))
            {
                Warnings.Add(warning);
            }
        }

        /// <summary>
        /// Adds multiple warnings to the validation result
        /// </summary>
        /// <param name="warnings">Warning descriptions</param>
        public void AddWarnings(IEnumerable<string> warnings)
        {
            foreach (var warning in warnings.Where(w => !string.IsNullOrWhiteSpace(w)))
            {
                AddWarning(warning);
            }
        }

        /// <summary>
        /// Adds a recommendation to the validation result
        /// </summary>
        /// <param name="recommendation">Recommendation description</param>
        public void AddRecommendation(string recommendation)
        {
            if (!string.IsNullOrWhiteSpace(recommendation))
            {
                Recommendations.Add(recommendation);
            }
        }

        /// <summary>
        /// Adds multiple recommendations to the validation result
        /// </summary>
        /// <param name="recommendations">Recommendation descriptions</param>
        public void AddRecommendations(IEnumerable<string> recommendations)
        {
            foreach (var recommendation in recommendations.Where(r => !string.IsNullOrWhiteSpace(r)))
            {
                AddRecommendation(recommendation);
            }
        }

        /// <summary>
        /// Adds context information to the validation result
        /// </summary>
        /// <param name="key">Context key</param>
        /// <param name="value">Context value</param>
        public void AddContext(string key, object value)
        {
            if (!string.IsNullOrWhiteSpace(key))
            {
                Context[key] = value;
            }
        }

        /// <summary>
        /// Merges another validation result into this one
        /// </summary>
        /// <param name="other">Other validation result to merge</param>
        public void Merge(ValidationResult other)
        {
            if (other == null) return;

            // Merge validity status
            IsValid = IsValid && other.IsValid;

            // Merge issues, warnings, and recommendations
            AddIssues(other.Issues);
            AddWarnings(other.Warnings);
            AddRecommendations(other.Recommendations);

            // Merge context
            foreach (var kvp in other.Context)
            {
                Context[kvp.Key] = kvp.Value;
            }
        }

        /// <summary>
        /// Creates a copy of this validation result
        /// </summary>
        /// <returns>Copy of the validation result</returns>
        public ValidationResult Clone()
        {
            return new ValidationResult
            {
                IsValid = IsValid,
                Issues = new List<string>(Issues),
                Warnings = new List<string>(Warnings),
                Recommendations = new List<string>(Recommendations),
                Context = new Dictionary<string, object>(Context),
                ValidationTime = ValidationTime
            };
        }

        /// <summary>
        /// Creates a successful validation result
        /// </summary>
        /// <returns>Valid validation result</returns>
        public static ValidationResult Success()
        {
            return new ValidationResult { IsValid = true };
        }

        /// <summary>
        /// Creates a failed validation result with an issue
        /// </summary>
        /// <param name="issue">Critical issue description</param>
        /// <returns>Invalid validation result</returns>
        public static ValidationResult Failure(string issue)
        {
            var result = new ValidationResult { IsValid = false };
            result.AddIssue(issue);
            return result;
        }

        /// <summary>
        /// Creates a validation result with warnings only
        /// </summary>
        /// <param name="warnings">Warning descriptions</param>
        /// <returns>Valid validation result with warnings</returns>
        public static ValidationResult WithWarnings(params string[] warnings)
        {
            var result = new ValidationResult { IsValid = true };
            result.AddWarnings(warnings);
            return result;
        }

        /// <summary>
        /// Gets a user-friendly message for display in UI
        /// </summary>
        /// <param name="includeDetails">Whether to include detailed information</param>
        /// <returns>User-friendly message</returns>
        public string GetUserMessage(bool includeDetails = false)
        {
            if (IsValid && !HasIssues)
            {
                return "Validation completed successfully.";
            }

            var message = new List<string>();

            if (!IsValid)
            {
                message.Add($"Validation failed with {Issues.Count} critical issue(s).");
                
                if (includeDetails && Issues.Count > 0)
                {
                    message.Add("");
                    message.Add("Issues:");
                    message.AddRange(Issues.Take(3).Select(i => $"• {i}"));
                    
                    if (Issues.Count > 3)
                    {
                        message.Add($"• ... and {Issues.Count - 3} more issue(s)");
                    }
                }
            }
            else if (HasWarningsOnly)
            {
                message.Add($"Validation completed with {Warnings.Count} warning(s).");
                
                if (includeDetails && Warnings.Count > 0)
                {
                    message.Add("");
                    message.Add("Warnings:");
                    message.AddRange(Warnings.Take(3).Select(w => $"• {w}"));
                    
                    if (Warnings.Count > 3)
                    {
                        message.Add($"• ... and {Warnings.Count - 3} more warning(s)");
                    }
                }
            }

            if (includeDetails && Recommendations.Count > 0)
            {
                message.Add("");
                message.Add("Recommendations:");
                message.AddRange(Recommendations.Take(2).Select(r => $"• {r}"));
                
                if (Recommendations.Count > 2)
                {
                    message.Add($"• ... and {Recommendations.Count - 2} more recommendation(s)");
                }
            }

            return string.Join(Environment.NewLine, message);
        }

        /// <summary>
        /// Returns a string representation of the validation result
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ValidationResult: {(IsValid ? "Valid" : "Invalid")}, Issues: {Issues.Count}, Warnings: {Warnings.Count}";
        }
    }
}
