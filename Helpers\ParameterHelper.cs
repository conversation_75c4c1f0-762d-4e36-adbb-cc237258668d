using System;
using System.Collections.Generic;
using System.Linq;
using Autodesk.Revit.DB;
using BecaRevitUtilities.Extensions;
using MEP.Pacifire.Models;
using PF_ServiceType = MEP.Pacifire.Models.PF_ServiceType;

namespace MEP.Pacifire.Helpers
{
    /// <summary>
    /// Helper class for extracting and managing Revit element parameters.
    /// Handles parameter extraction for fire stopping, service, and structural elements.
    /// </summary>
    public class ParameterHelper : IParameterHelper
    {
        private Dictionary<string, string> _customParameterMappings = new();

        /// <summary>
        /// Extracts fire stopping specific parameters from a Revit element
        /// </summary>
        public void ExtractFireStoppingParameters(Element element, FireStoppingElement fireStoppingElement)
        {
            try
            {
                // Basic element information
                fireStoppingElement.FamilyName = GetFamilyName(element);
                fireStoppingElement.TypeName = GetTypeName(element);
                fireStoppingElement.Category = GetCategoryName(element);
                fireStoppingElement.LevelName = GetLevelName(element);

                // Beca specific parameters
                fireStoppingElement.BecaTypeMark = GetTypeParameterValueAsString(element, "Beca Type Mark");
                fireStoppingElement.BecaInstMark = GetParameterValueAsString(element, "Beca Inst Mark");
                fireStoppingElement.BecaSystemDescription = GetParameterValueAsString(element, "Beca System Description");
                fireStoppingElement.BecaFamilyMaterial = GetParameterValueAsString(element, "Beca Family Material");
                fireStoppingElement.BecaFreeSize = GetParameterValueAsString(element, "Beca Free Size");
                fireStoppingElement.BecaFamilyOrientation = GetParameterValueAsString(element, "Beca Family Orientation");
                fireStoppingElement.BecaFamilyReference = GetParameterValueAsString(element, "Beca Family Reference");

                // Extract any custom parameters
                ExtractCustomParameters(element, fireStoppingElement.CustomParameters);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting fire stopping parameters: {ex.Message}");
            }
        }

        /// <summary>
        /// Extracts service specific parameters from a Revit element
        /// </summary>
        public void ExtractServiceParameters(Element element, ServiceElement serviceElement)
        {
            try
            {
                // Basic element information
                serviceElement.Category = GetCategoryName(element);
                serviceElement.LevelName = GetLevelName(element);
                serviceElement.TypeName = GetTypeName(element);

                // Determine service type
                serviceElement.ServiceType = DetermineServiceType(element);

                // Size information
                serviceElement.Size = GetServiceSize(element, serviceElement.ServiceType);

                // Material information
                serviceElement.Material = GetMaterialName(element);

                // System information
                serviceElement.SystemType = GetSystemType(element);
                serviceElement.SystemName = GetParameterValueAsString(element, "System Name");

                // Additional service parameters
                serviceElement.InsulationThickness = GetParameterValueAsDouble(element, "Insulation Thickness");
                serviceElement.WorkingPressure = GetParameterValueAsDouble(element, "Working Pressure");
                serviceElement.FlowRate = GetParameterValueAsDouble(element, "Flow");
                serviceElement.Temperature = GetParameterValueAsDouble(element, "Temperature");

                // Extract any custom parameters
                ExtractCustomParameters(element, serviceElement.CustomParameters);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting service parameters: {ex.Message}");
            }
        }

        /// <summary>
        /// Extracts structural specific parameters from a Revit element
        /// </summary>
        public void ExtractStructuralParameters(Element element, StructuralElement structuralElement)
        {
            try
            {
                // Basic element information
                structuralElement.FamilyName = GetFamilyName(element);
                structuralElement.TypeName = GetTypeName(element);
                structuralElement.Category = GetCategoryName(element);
                structuralElement.LevelName = GetLevelName(element);

                // Determine structure type
                structuralElement.StructureType = DetermineStructureType(element);

                // Material and construction information
                structuralElement.MaterialType = GetMaterialName(element);
                structuralElement.ConstructionType = GetParameterValueAsString(element, "Construction Type");
                structuralElement.Function = GetParameterValueAsString(element, "Function");
                structuralElement.StructuralUsage = GetParameterValueAsString(element, "Structural Usage");

                // Fire rating and thickness
                structuralElement.FireRating = GetFireRating(element);
                structuralElement.Thickness = GetThickness(element);

                // Assembly information
                structuralElement.AssemblyCode = GetParameterValueAsString(element, "Assembly Code");
                structuralElement.Comments = GetParameterValueAsString(element, "Comments");

                // Extract any custom parameters
                ExtractCustomParameters(element, structuralElement.CustomParameters);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting structural parameters: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets an instance parameter value as string from an element
        /// </summary>
        public string GetParameterValueAsString(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter != null && parameter.HasValue)
                {
                    // Explicitly specify the return type for each case to resolve CS8506
                    return parameter.StorageType switch
                    {
                        StorageType.String => parameter.AsString() ?? string.Empty,
                        StorageType.Integer => parameter.AsInteger().ToString(),
                        StorageType.Double => parameter.AsDouble().ToString("F2"),
                        StorageType.ElementId => parameter.AsElementId().ToString(),
                        _ => string.Empty
                    };
                }
                return string.Empty;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets a type parameter value as string from an element
        /// </summary>
        public string GetTypeParameterValueAsString(Element element, string parameterName)
        {
            try
            {
                // Get the type of the element
                ElementId typeId = element.GetTypeId(); // Retrieves the type ID
                if (typeId == ElementId.InvalidElementId) return string.Empty;

                Element elementType = element.Document.GetElement(typeId); // Get the element type
                if (elementType == null) return string.Empty;

                // Find the type parameter by name
                var parameter = GetParameter(elementType, parameterName);

                return parameter?.AsString() ?? parameter?.AsValueString() ?? string.Empty;

            }
            catch (Exception)
            {
                return string.Empty; // Return empty string if any errors occur
            }
        }

        /// <summary>
        /// Gets a parameter value as double from an element
        /// </summary>
        public double GetParameterValueAsDouble(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter != null && parameter.HasValue)
                {
                    // Explicitly specify the return type for each case to resolve CS8506
                    return parameter.StorageType switch
                    {
                        StorageType.Double => parameter.AsDouble(),
                        StorageType.Integer => (double)parameter.AsInteger(),
                        StorageType.String when double.TryParse(parameter.AsString(), out var result) => result,
                        _ => 0.0
                    };
                }
                return 0.0;
            }
            catch (Exception)
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Gets a parameter value as integer from an element
        /// </summary>
        public int GetParameterValueAsInteger(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter != null && parameter.HasValue)
                {
                    // Explicitly specify the return type for each case to resolve CS8506
                    return parameter.StorageType switch
                    {
                        StorageType.Integer => parameter.AsInteger(),
                        StorageType.Double => (int)parameter.AsDouble(),
                        StorageType.String when int.TryParse(parameter.AsString(), out var result) => result,
                        _ => 0
                    };
                }
                return 0;
            }
            catch (Exception)
            {
                return 0;
            }
        }

        /// <summary>
        /// Gets a parameter value as ElementId from an element
        /// </summary>
        public ElementId GetParameterValueAsElementId(Element element, string parameterName)
        {
            try
            {
                var parameter = GetParameter(element, parameterName);
                if (parameter != null && parameter.HasValue && parameter.StorageType == StorageType.ElementId)
                {
                    return parameter.AsElementId();
                }
                return ElementId.InvalidElementId;
            }
            catch (Exception)
            {
                return ElementId.InvalidElementId;
            }
        }

        /// <summary>
        /// Checks if an element has a specific parameter
        /// </summary>
        public bool HasParameter(Element element, string parameterName)
        {
            return GetParameter(element, parameterName) != null;
        }

        /// <summary>
        /// Gets all parameter names and values from an element
        /// </summary>
        public Dictionary<string, object> GetAllParameters(Element element)
        {
            var parameters = new Dictionary<string, object>();

            try
            {
                foreach (Parameter parameter in element.Parameters)
                {
                    if (parameter.HasValue)
                    {
                        var name = parameter.Definition.Name;

                        object value = null;

                        switch (parameter.StorageType)
                        {
                            case StorageType.None:
                                value = null;
                                break;
                            case StorageType.Integer:
                                value = parameter.AsInteger();
                                break;
                            case StorageType.Double:
                                value = parameter.AsDouble();
                                break;
                            case StorageType.String:
                                value = parameter.AsString();
                                break;
                            case StorageType.ElementId:
                                value = parameter.AsElementId();
                                break;
                            default:
                                break;
                        }

                        if (value != null)
                        {
                            parameters[name] = value;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting all parameters: {ex.Message}");
            }

            return parameters;
        }

        /// <summary>
        /// Gets the family name from an element
        /// </summary>
        public string GetFamilyName(Element element)
        {
            try
            {
                if (element.Document.GetElement(element.GetTypeId()) is FamilySymbol familySymbol)
                {
                    return familySymbol.FamilyName;
                }
                return element.Name;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the type name from an element
        /// </summary>
        public string GetTypeName(Element element)
        {
            try
            {
                var elementType = element.Document.GetElement(element.GetTypeId());
                return elementType?.Name ?? string.Empty;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the category name from an element
        /// </summary>
        public string GetCategoryName(Element element)
        {
            try
            {
                return element.Category?.Name ?? string.Empty;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the level name from an element
        /// </summary>
        public string GetLevelName(Element element)
        {
            try
            {
                var levelId = GetParameterValueAsElementId(element, "Level");
                if (levelId != ElementId.InvalidElementId)
                {
                    var level = element.Document.GetElement(levelId) as Level;
                    return level?.Name ?? string.Empty;
                }

                // Fallback: try to get level from location
                if (element.LevelId != ElementId.InvalidElementId)
                {
                    var level = element.Document.GetElement(element.LevelId) as Level;
                    return level?.Name ?? string.Empty;
                }

                return string.Empty;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Determines the service type from an element
        /// </summary>
        public PF_ServiceType DetermineServiceType(Element element)
        {
            try
            {
                var categoryId = element.Category?.Id.IntegerValue();
                return categoryId switch
                {
                    (int)BuiltInCategory.OST_DuctFitting => PF_ServiceType.Duct,
                    (int)BuiltInCategory.OST_DuctCurves => PF_ServiceType.Duct,
                    (int)BuiltInCategory.OST_FlexDuctCurves => PF_ServiceType.FlexDuct,
                    (int)BuiltInCategory.OST_PipeFitting => PF_ServiceType.Pipe,
                    (int)BuiltInCategory.OST_PipeCurves => PF_ServiceType.Pipe,
                    (int)BuiltInCategory.OST_FlexPipeCurves => PF_ServiceType.FlexPipe,
                    (int)BuiltInCategory.OST_CableTrayFitting => PF_ServiceType.CableTray,
                    (int)BuiltInCategory.OST_CableTray => PF_ServiceType.CableTray,
                    (int)BuiltInCategory.OST_ConduitFitting => PF_ServiceType.Conduit,
                    (int)BuiltInCategory.OST_Conduit => PF_ServiceType.Conduit,
                    _ => PF_ServiceType.Unknown
                };
            }
            catch (Exception)
            {
                return PF_ServiceType.Unknown;
            }
        }

        /// <summary>
        /// Determines the structure type from an element
        /// </summary>
        public StructureType DetermineStructureType(Element element)
        {
            try
            {
                var categoryId = element.Category?.Id.IntegerValue();
                return categoryId switch
                {
                    (int)BuiltInCategory.OST_Walls => StructureType.Wall,
                    (int)BuiltInCategory.OST_Floors => StructureType.Floor,
                    (int)BuiltInCategory.OST_Ceilings => StructureType.Ceiling,
                    (int)BuiltInCategory.OST_Roofs => StructureType.Roof,
                    (int)BuiltInCategory.OST_StructuralFoundation => StructureType.Foundation,
                    (int)BuiltInCategory.OST_StructuralFraming => StructureType.Beam,
                    (int)BuiltInCategory.OST_StructuralColumns => StructureType.Column,
                    _ => StructureType.Unknown
                };
            }
            catch (Exception)
            {
                return StructureType.Unknown;
            }
        }

        /// <summary>
        /// Gets the size parameter for a service element
        /// </summary>
        public string GetServiceSize(Element element, PF_ServiceType serviceType)
        {
            try
            {
                return serviceType switch
                {
                    PF_ServiceType.Duct => GetParameterValueAsString(element, "Size") ?? 
                                       GetParameterValueAsString(element, "Width") + " x " + GetParameterValueAsString(element, "Height"),
                    PF_ServiceType.Pipe => GetParameterValueAsString(element, "Diameter") ?? 
                                       GetParameterValueAsString(element, "Size"),
                    PF_ServiceType.CableTray => GetParameterValueAsString(element, "Width") + " x " + GetParameterValueAsString(element, "Height"),
                    PF_ServiceType.Conduit => GetParameterValueAsString(element, "Diameter") ?? 
                                          GetParameterValueAsString(element, "Size"),
                    _ => GetParameterValueAsString(element, "Size")
                };
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the material parameter for an element
        /// </summary>
        public string GetMaterialName(Element element)
        {
            try
            {
                //var materialId = GetParameterValueAsElementId(element, "Material");
                //if (materialId != ElementId.InvalidElementId)
                //{
                //    var material = element.Document.GetElement(materialId) as Material;
                //    return material?.Name ?? string.Empty;
                //}

                // Fallback to material parameter as string
                return GetTypeParameterValueAsString(element, "Description");
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the system type for a service element
        /// </summary>
        public string GetSystemType(Element element)
        {
            try
            {
                return GetParameterValueAsString(element, "System Type")
                       ?? GetParameterValueAsString(element, "Service Type")
                       ?? string.Empty;
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the fire rating for a structural element
        /// </summary>
        public string GetFireRating(Element element)
        {
            try
            {
                return GetTypeParameterValueAsString(element, "Fire Rating") ??
                       GetTypeParameterValueAsString(element, "Fire Rating NDH");
            }
            catch (Exception)
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets the thickness for a structural element
        /// </summary>
        public double GetThickness(Element element)
        {
            try
            {
                // Convert from feet to millimeters
                var width = GetParameterValueAsDouble(element, "Width");
                if (width != 0.0)
                {
                    return width * 304.8; // Convert feet to mm
                }

                var thickness = GetParameterValueAsDouble(element, "Thickness");
                return thickness * 304.8; // Convert feet to mm
            }
            catch (Exception)
            {
                return 0.0;
            }
        }

        /// <summary>
        /// Validates that required parameters are present for fire stopping analysis
        /// </summary>
        public ParameterValidationResult ValidateFireStoppingParameters(Element element)
        {
            var result = new ParameterValidationResult { IsValid = true };

            var requiredParameters = new[]
            {
                "Beca Type Mark",
                "Family Name",
                "Type Name"
            };

            foreach (var paramName in requiredParameters)
            {
                if (!HasParameter(element, paramName) || string.IsNullOrEmpty(GetParameterValueAsString(element, paramName)))
                {
                    result.MissingParameters.Add(paramName);
                    result.IsValid = false;
                }
            }

            var optionalParameters = new[]
            {
                "Beca Inst Mark",
                "Beca System Description",
                "Beca Family Material"
            };

            foreach (var paramName in optionalParameters)
            {
                if (!HasParameter(element, paramName))
                {
                    result.Warnings.Add($"Optional parameter '{paramName}' not found");
                }
            }

            return result;
        }

        /// <summary>
        /// Gets custom parameter mappings for extensibility
        /// </summary>
        public Dictionary<string, string> GetCustomParameterMappings()
        {
            return new Dictionary<string, string>(_customParameterMappings);
        }

        /// <summary>
        /// Sets custom parameter mappings for extensibility
        /// </summary>
        public void SetCustomParameterMappings(Dictionary<string, string> mappings)
        {
            _customParameterMappings = mappings ?? new Dictionary<string, string>();
        }

        #region Private Helper Methods

        private Parameter? GetParameter(Element element, string parameterName)
        {
            try
            {
                // Try to get parameter by name
                foreach (Parameter parameter in element.Parameters)
                {
                    if (parameter.Definition.Name.Equals(parameterName, StringComparison.OrdinalIgnoreCase))
                    {
                        return parameter;
                    }
                }

                // Check custom mappings
                if (_customParameterMappings.TryGetValue(parameterName, out var mappedName))
                {
                    foreach (Parameter parameter in element.Parameters)
                    {
                        if (parameter.Definition.Name.Equals(mappedName, StringComparison.OrdinalIgnoreCase))
                        {
                            return parameter;
                        }
                    }
                }

                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private void ExtractCustomParameters(Element element, Dictionary<string, object> customParameters)
        {
            try
            {
                foreach (var mapping in _customParameterMappings)
                {
                    var value = GetParameterValueAsString(element, mapping.Key);
                    if (!string.IsNullOrEmpty(value))
                    {
                        customParameters[mapping.Key] = value;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error extracting custom parameters: {ex.Message}");
            }
        }


        #endregion
    }
}
