using System;
using System.Diagnostics;

namespace MEP.Pacifire.Utilities
{
    /// <summary>
    /// Performance timing utility for measuring operation durations.
    /// Provides easy-to-use timing functionality with automatic logging.
    /// </summary>
    public class PerformanceTimer : IDisposable
    {
        private readonly Stopwatch _stopwatch;
        private readonly string _operationName;
        private readonly int _itemCount;
        private readonly bool _autoLog;
        private bool _disposed = false;

        /// <summary>
        /// Initializes a new performance timer
        /// </summary>
        /// <param name="operationName">Name of the operation being timed</param>
        /// <param name="itemCount">Number of items being processed (optional)</param>
        /// <param name="autoLog">Whether to automatically log results on disposal</param>
        public PerformanceTimer(string operationName, int itemCount = 0, bool autoLog = true)
        {
            _operationName = operationName ?? throw new ArgumentNullException(nameof(operationName));
            _itemCount = itemCount;
            _autoLog = autoLog;
            _stopwatch = Stopwatch.StartNew();

            if (_autoLog)
            {
                LoggingHelper.LogDebug($"Started timing: {_operationName}");
            }
        }

        /// <summary>
        /// Gets the elapsed time since the timer was started
        /// </summary>
        public TimeSpan Elapsed => _stopwatch.Elapsed;

        /// <summary>
        /// Gets the elapsed milliseconds since the timer was started
        /// </summary>
        public double ElapsedMilliseconds => _stopwatch.Elapsed.TotalMilliseconds;

        /// <summary>
        /// Stops the timer and returns the elapsed time
        /// </summary>
        /// <returns>Elapsed time</returns>
        public TimeSpan Stop()
        {
            _stopwatch.Stop();
            return _stopwatch.Elapsed;
        }

        /// <summary>
        /// Restarts the timer
        /// </summary>
        public void Restart()
        {
            _stopwatch.Restart();
            if (_autoLog)
            {
                LoggingHelper.LogDebug($"Restarted timing: {_operationName}");
            }
        }

        /// <summary>
        /// Gets performance statistics
        /// </summary>
        /// <returns>Performance statistics string</returns>
        public string GetPerformanceStats()
        {
            var elapsed = _stopwatch.Elapsed;
            if (_itemCount > 0)
            {
                var perItem = elapsed.TotalMilliseconds / _itemCount;
                return $"{_operationName}: {elapsed.TotalMilliseconds:F2}ms total, {_itemCount} items, {perItem:F2}ms per item";
            }
            else
            {
                return $"{_operationName}: {elapsed.TotalMilliseconds:F2}ms";
            }
        }

        /// <summary>
        /// Logs the current performance statistics
        /// </summary>
        public void LogCurrentStats()
        {
            LoggingHelper.LogPerformance(_operationName, _stopwatch.Elapsed, _itemCount);
        }

        /// <summary>
        /// Disposes the timer and logs results if auto-logging is enabled
        /// </summary>
        public void Dispose()
        {
            if (!_disposed)
            {
                _stopwatch.Stop();
                
                if (_autoLog)
                {
                    LoggingHelper.LogPerformance(_operationName, _stopwatch.Elapsed, _itemCount);
                }

                _disposed = true;
            }
        }

        /// <summary>
        /// Creates a new performance timer and starts timing
        /// </summary>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="itemCount">Number of items being processed</param>
        /// <param name="autoLog">Whether to automatically log results</param>
        /// <returns>New performance timer</returns>
        public static PerformanceTimer StartNew(string operationName, int itemCount = 0, bool autoLog = true)
        {
            return new PerformanceTimer(operationName, itemCount, autoLog);
        }

        /// <summary>
        /// Times an action and returns the elapsed time
        /// </summary>
        /// <param name="action">Action to time</param>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Elapsed time</returns>
        public static TimeSpan Time(Action action, string operationName = "Operation")
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));

            using var timer = new PerformanceTimer(operationName, autoLog: false);
            action();
            return timer.Stop();
        }

        /// <summary>
        /// Times an action with automatic logging
        /// </summary>
        /// <param name="action">Action to time</param>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="itemCount">Number of items being processed</param>
        /// <returns>Elapsed time</returns>
        public static TimeSpan TimeAndLog(Action action, string operationName = "Operation", int itemCount = 0)
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));

            using var timer = new PerformanceTimer(operationName, itemCount, autoLog: true);
            action();
            return timer.Stop();
        }

        /// <summary>
        /// Times an async action and returns the elapsed time
        /// </summary>
        /// <param name="asyncAction">Async action to time</param>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Elapsed time</returns>
        public static async System.Threading.Tasks.Task<TimeSpan> TimeAsync(
            Func<System.Threading.Tasks.Task> asyncAction, 
            string operationName = "Async Operation")
        {
            if (asyncAction == null)
                throw new ArgumentNullException(nameof(asyncAction));

            using var timer = new PerformanceTimer(operationName, autoLog: false);
            await asyncAction();
            return timer.Stop();
        }

        /// <summary>
        /// Times an async action with automatic logging
        /// </summary>
        /// <param name="asyncAction">Async action to time</param>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="itemCount">Number of items being processed</param>
        /// <returns>Elapsed time</returns>
        public static async System.Threading.Tasks.Task<TimeSpan> TimeAndLogAsync(
            Func<System.Threading.Tasks.Task> asyncAction, 
            string operationName = "Async Operation", 
            int itemCount = 0)
        {
            if (asyncAction == null)
                throw new ArgumentNullException(nameof(asyncAction));

            using var timer = new PerformanceTimer(operationName, itemCount, autoLog: true);
            await asyncAction();
            return timer.Stop();
        }
    }

    /// <summary>
    /// Extension methods for performance timing
    /// </summary>
    public static class PerformanceTimerExtensions
    {
        /// <summary>
        /// Times the execution of an action
        /// </summary>
        /// <param name="action">Action to time</param>
        /// <param name="operationName">Name of the operation</param>
        /// <returns>Elapsed time</returns>
        public static TimeSpan Time(this Action action, string operationName = "Operation")
        {
            return PerformanceTimer.Time(action, operationName);
        }

        /// <summary>
        /// Times the execution of an action with logging
        /// </summary>
        /// <param name="action">Action to time</param>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="itemCount">Number of items being processed</param>
        /// <returns>Elapsed time</returns>
        public static TimeSpan TimeAndLog(this Action action, string operationName = "Operation", int itemCount = 0)
        {
            return PerformanceTimer.TimeAndLog(action, operationName, itemCount);
        }
    }
}
