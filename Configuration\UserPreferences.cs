using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using MEP.Pacifire.Models;
using MEP.Pacifire.Services;

namespace MEP.Pacifire.Configuration
{
    /// <summary>
    /// User preferences manager that provides strongly-typed access to user settings
    /// with change notification support for UI binding.
    /// </summary>
    public class UserPreferences : INotifyPropertyChanged
    {
        private readonly AppSettings _appSettings;
        private static UserPreferences? _instance;
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets the singleton instance of UserPreferences
        /// </summary>
        public static UserPreferences Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new UserPreferences();
                    }
                }
                return _instance;
            }
        }

        private UserPreferences()
        {
            _appSettings = AppSettings.Instance;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        #region UI Preferences

        /// <summary>
        /// Whether the sidebar is collapsed
        /// </summary>
        public bool IsSidebarCollapsed
        {
            get => _appSettings.IsSidebarCollapsed;
            set
            {
                if (_appSettings.IsSidebarCollapsed != value)
                {
                    _appSettings.IsSidebarCollapsed = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        /// <summary>
        /// Application theme
        /// </summary>
        public string Theme
        {
            get => _appSettings.Theme;
            set
            {
                if (_appSettings.Theme != value)
                {
                    _appSettings.Theme = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        /// <summary>
        /// Window width
        /// </summary>
        public double WindowWidth
        {
            get => _appSettings.WindowWidth;
            set
            {
                if (Math.Abs(_appSettings.WindowWidth - value) > 0.1)
                {
                    _appSettings.WindowWidth = value;
                    OnPropertyChanged();
                    // Don't save immediately for window size changes to avoid excessive I/O
                }
            }
        }

        /// <summary>
        /// Window height
        /// </summary>
        public double WindowHeight
        {
            get => _appSettings.WindowHeight;
            set
            {
                if (Math.Abs(_appSettings.WindowHeight - value) > 0.1)
                {
                    _appSettings.WindowHeight = value;
                    OnPropertyChanged();
                    // Don't save immediately for window size changes to avoid excessive I/O
                }
            }
        }

        /// <summary>
        /// Window state
        /// </summary>
        public string WindowState
        {
            get => _appSettings.WindowState;
            set
            {
                if (_appSettings.WindowState != value)
                {
                    _appSettings.WindowState = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        #endregion

        #region Analysis Preferences

        /// <summary>
        /// Default adjacency threshold in millimeters
        /// </summary>
        public double AdjacencyThreshold
        {
            get => _appSettings.DefaultAdjacencyThreshold;
            set
            {
                if (Math.Abs(_appSettings.DefaultAdjacencyThreshold - value) > 0.1)
                {
                    _appSettings.DefaultAdjacencyThreshold = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        /// <summary>
        /// Connection tolerance in millimeters
        /// </summary>
        public double ConnectionTolerance
        {
            get => _appSettings.DefaultConnectionTolerance;
            set
            {
                if (Math.Abs(_appSettings.DefaultConnectionTolerance - value) > 0.1)
                {
                    _appSettings.DefaultConnectionTolerance = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        /// <summary>
        /// Enable spatial indexing optimization
        /// </summary>
        public bool EnableSpatialIndexing
        {
            get => _appSettings.EnableSpatialIndexing;
            set
            {
                if (_appSettings.EnableSpatialIndexing != value)
                {
                    _appSettings.EnableSpatialIndexing = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        /// <summary>
        /// Enable multi-threading
        /// </summary>
        public bool EnableMultiThreading
        {
            get => _appSettings.EnableMultiThreading;
            set
            {
                if (_appSettings.EnableMultiThreading != value)
                {
                    _appSettings.EnableMultiThreading = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        #endregion

        #region Export Preferences

        /// <summary>
        /// Default export directory
        /// </summary>
        public string DefaultExportDirectory
        {
            get => _appSettings.DefaultExportDirectory;
            set
            {
                if (_appSettings.DefaultExportDirectory != value)
                {
                    _appSettings.DefaultExportDirectory = value ?? string.Empty;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        /// <summary>
        /// Gets default export settings based on user preferences
        /// </summary>
        public ExportSettings GetDefaultExportSettings()
        {
            return new ExportSettings
            {
                IncludeFireStoppingDetails = _appSettings.DefaultIncludeFireStoppingDetails,
                IncludeServiceDetails = _appSettings.DefaultIncludeServiceDetails,
                IncludeStructureDetails = _appSettings.DefaultIncludeStructureDetails,
                IncludeDesignChecks = _appSettings.DefaultIncludeDesignChecks,
                ApplyFormatting = _appSettings.DefaultApplyFormatting,
                AutoFitColumns = _appSettings.DefaultAutoFitColumns,
                AddFilters = _appSettings.DefaultAddFilters,
                UserName = Environment.UserName
            };
        }

        #endregion

        #region Filter Preferences

        /// <summary>
        /// Whether to remember last used filters
        /// </summary>
        public bool RememberLastFilters
        {
            get => _appSettings.RememberLastFilters;
            set
            {
                if (_appSettings.RememberLastFilters != value)
                {
                    _appSettings.RememberLastFilters = value;
                    OnPropertyChanged();
                    SaveSettings();
                }
            }
        }

        /// <summary>
        /// Saves current filter state
        /// </summary>
        /// <param name="filterSettings">Filter settings to save</param>
        public void SaveLastFilters(FilterSettings filterSettings)
        {
            if (!RememberLastFilters) return;

            try
            {
                _appSettings.LastSelectedLevels.Clear();
                foreach (var level in filterSettings.SelectedLevels.Where(x => x.IsSelected))
                {
                    _appSettings.LastSelectedLevels.Add(level.Name);
                }

                _appSettings.LastSelectedCategories.Clear();
                foreach (var category in filterSettings.SelectedCategories.Where(x => x.IsSelected))
                {
                    _appSettings.LastSelectedCategories.Add(category.Name);
                }

                _appSettings.LastSelectedLinkedModels.Clear();
                foreach (var model in filterSettings.SelectedLinkedModels.Where(x => x.IsSelected))
                {
                    _appSettings.LastSelectedLinkedModels.Add(model.Name);
                }

                _appSettings.LastSearchText = filterSettings.SearchText;
                _appSettings.LastShowFailuresOnly = filterSettings.ShowFailuresOnly;

                SaveSettings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving last filters: {ex.Message}");
            }
        }

        /// <summary>
        /// Applies saved filter state to filter settings
        /// </summary>
        /// <param name="filterSettings">Filter settings to update</param>
        public void ApplyLastFilters(FilterSettings filterSettings)
        {
            if (!RememberLastFilters) return;

            try
            {
                // Apply level selections
                foreach (var level in filterSettings.SelectedLevels)
                {
                    level.IsSelected = _appSettings.LastSelectedLevels.Contains(level.Name);
                }

                // Apply category selections
                foreach (var category in filterSettings.SelectedCategories)
                {
                    category.IsSelected = _appSettings.LastSelectedCategories.Contains(category.Name);
                }

                // Apply linked model selections
                foreach (var model in filterSettings.SelectedLinkedModels)
                {
                    model.IsSelected = _appSettings.LastSelectedLinkedModels.Contains(model.Name);
                }

                // Apply other settings
                filterSettings.SearchText = _appSettings.LastSearchText;
                filterSettings.ShowFailuresOnly = _appSettings.LastShowFailuresOnly;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying last filters: {ex.Message}");
            }
        }

        #endregion

        #region Custom Parameters

        /// <summary>
        /// Gets custom parameter mappings
        /// </summary>
        public Dictionary<string, string> CustomParameterMappings => _appSettings.CustomParameterMappings;

        /// <summary>
        /// Adds or updates a custom parameter mapping
        /// </summary>
        /// <param name="displayName">Display name for the parameter</param>
        /// <param name="revitParameterName">Actual Revit parameter name</param>
        public void SetCustomParameterMapping(string displayName, string revitParameterName)
        {
            if (string.IsNullOrEmpty(displayName) || string.IsNullOrEmpty(revitParameterName))
                return;

            _appSettings.CustomParameterMappings[displayName] = revitParameterName;
            SaveSettings();
        }

        /// <summary>
        /// Removes a custom parameter mapping
        /// </summary>
        /// <param name="displayName">Display name to remove</param>
        public void RemoveCustomParameterMapping(string displayName)
        {
            if (_appSettings.CustomParameterMappings.Remove(displayName))
            {
                SaveSettings();
            }
        }

        #endregion

        #region Methods

        /// <summary>
        /// Saves window position and size (called when window is closing)
        /// </summary>
        public void SaveWindowState()
        {
            SaveSettings();
        }

        /// <summary>
        /// Resets all preferences to default values
        /// </summary>
        public void ResetToDefaults()
        {
            _appSettings.ResetToDefaults();
            SaveSettings();
            
            // Notify all properties changed
            OnPropertyChanged(string.Empty);
        }

        /// <summary>
        /// Validates and corrects preference values
        /// </summary>
        public void ValidateAndCorrect()
        {
            _appSettings.ValidateAndCorrect();
            SaveSettings();
        }

        /// <summary>
        /// Gets a summary of current preferences
        /// </summary>
        public string GetPreferencesSummary()
        {
            return $"Theme: {Theme}, " +
                   $"Sidebar: {(IsSidebarCollapsed ? "Collapsed" : "Expanded")}, " +
                   $"Adjacency: {AdjacencyThreshold}mm, " +
                   $"Spatial Indexing: {(EnableSpatialIndexing ? "Enabled" : "Disabled")}, " +
                   $"Multi-threading: {(EnableMultiThreading ? "Enabled" : "Disabled")}";
        }

        private void SaveSettings()
        {
            try
            {
                _appSettings.Save();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving preferences: {ex.Message}");
            }
        }

        private void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}
