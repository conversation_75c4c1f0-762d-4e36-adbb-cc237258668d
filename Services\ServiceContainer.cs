using System;
using Microsoft.Extensions.DependencyInjection;
using Autodesk.Revit.DB;
using MEP.Pacifire.Services;
using MEP.Pacifire.ViewModels;
using MEP.Pacifire.Helpers;

namespace MEP.Pacifire.Services
{
    /// <summary>
    /// Service container for dependency injection configuration.
    /// Manages service registration and lifetime for the Fire Stopping Solution Exporter.
    /// </summary>
    public static class ServiceContainer
    {
        private static IServiceProvider? _serviceProvider;

        /// <summary>
        /// Configures and builds the service container
        /// </summary>
        /// <param name="document">Revit document instance</param>
        /// <returns>Configured service provider</returns>
        public static IServiceProvider ConfigureServices(Document document)
        {
            try
            {
                var services = new ServiceCollection();

                // Register Revit document as singleton
                services.AddSingleton(document);

                // Register helper services
                services.AddSingleton<IGeometryHelper, GeometryHelper>();
                services.AddSingleton<ISpatialHelper, SpatialHelper>();
                services.AddSingleton<IParameterHelper, ParameterHelper>();

                // Register business services
                services.AddScoped<IExtractionService, ExtractionService>();
                services.AddScoped<IDesignCheckService, DesignCheckService>();
                services.AddScoped<IExcelExportService, ExcelExportService>();

                // Register ViewModels
                services.AddTransient<MainViewModel>();

                _serviceProvider = services.BuildServiceProvider();

                return _serviceProvider;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error configuring services: {ex.Message}");
                throw new InvalidOperationException($"Failed to configure dependency injection container: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets a service from the container
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        public static T GetService<T>() where T : class
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("Service container has not been configured. Call ConfigureServices first.");
            }

            var service = _serviceProvider.GetService<T>();
            if (service == null)
            {
                throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered in the container.");
            }

            return service;
        }

        /// <summary>
        /// Gets a required service from the container
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <returns>Service instance</returns>
        /// <exception cref="InvalidOperationException">Thrown if service is not found</exception>
        public static T GetRequiredService<T>() where T : class
        {
            if (_serviceProvider == null)
            {
                throw new InvalidOperationException("Service container has not been configured. Call ConfigureServices first.");
            }

            return _serviceProvider.GetRequiredService<T>();
        }

        /// <summary>
        /// Gets a service from the container with fallback
        /// </summary>
        /// <typeparam name="T">Service type</typeparam>
        /// <param name="fallback">Fallback instance if service not found</param>
        /// <returns>Service instance or fallback</returns>
        public static T GetServiceOrDefault<T>(T fallback) where T : class
        {
            try
            {
                return GetService<T>();
            }
            catch
            {
                return fallback;
            }
        }

        /// <summary>
        /// Validates that all required services are properly registered
        /// </summary>
        /// <returns>True if all services are valid</returns>
        public static bool ValidateServices()
        {
            if (_serviceProvider == null)
            {
                return false;
            }

            try
            {
                // Validate core services
                var geometryHelper = _serviceProvider.GetService<IGeometryHelper>();
                var spatialHelper = _serviceProvider.GetService<ISpatialHelper>();
                var parameterHelper = _serviceProvider.GetService<IParameterHelper>();
                var extractionService = _serviceProvider.GetService<IExtractionService>();
                var designCheckService = _serviceProvider.GetService<IDesignCheckService>();
                var excelExportService = _serviceProvider.GetService<IExcelExportService>();
                var mainViewModel = _serviceProvider.GetService<MainViewModel>();

                // Check if all required services are available
                return geometryHelper != null &&
                       spatialHelper != null &&
                       parameterHelper != null &&
                       extractionService != null &&
                       designCheckService != null &&
                       excelExportService != null &&
                       mainViewModel != null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Service validation failed: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets validation details for troubleshooting
        /// </summary>
        /// <returns>Validation result with details</returns>
        public static ServiceValidationResult GetValidationDetails()
        {
            var result = new ServiceValidationResult();

            if (_serviceProvider == null)
            {
                result.IsValid = false;
                result.Issues.Add("Service provider is not configured");
                return result;
            }

            // Check each service individually
            CheckService<IGeometryHelper>("IGeometryHelper", result);
            CheckService<ISpatialHelper>("ISpatialHelper", result);
            CheckService<IParameterHelper>("IParameterHelper", result);
            CheckService<IExtractionService>("IExtractionService", result);
            CheckService<IDesignCheckService>("IDesignCheckService", result);
            CheckService<IExcelExportService>("IExcelExportService", result);
            CheckService<MainViewModel>("MainViewModel", result);
            CheckService<Document>("Document", result);

            result.IsValid = result.Issues.Count == 0;
            return result;
        }

        /// <summary>
        /// Disposes the service container and releases resources
        /// </summary>
        public static void Dispose()
        {
            try
            {
                if (_serviceProvider is IDisposable disposableProvider)
                {
                    disposableProvider.Dispose();
                }
                _serviceProvider = null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing service container: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks if the service container is configured
        /// </summary>
        public static bool IsConfigured => _serviceProvider != null;

        /// <summary>
        /// Gets the current service provider (for advanced scenarios)
        /// </summary>
        public static IServiceProvider? ServiceProvider => _serviceProvider;

        #region Private Helper Methods

        private static void CheckService<T>(string serviceName, ServiceValidationResult result)
        {
            try
            {
                var service = _serviceProvider!.GetService<T>();
                if (service == null)
                {
                    result.Issues.Add($"Service '{serviceName}' is not registered or could not be resolved");
                }
                else
                {
                    result.RegisteredServices.Add(serviceName);
                }
            }
            catch (Exception ex)
            {
                result.Issues.Add($"Error resolving service '{serviceName}': {ex.Message}");
            }
        }

        #endregion
    }

    /// <summary>
    /// Result of service validation
    /// </summary>
    public class ServiceValidationResult
    {
        /// <summary>
        /// Indicates if all services are valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation issues
        /// </summary>
        public List<string> Issues { get; set; } = new();

        /// <summary>
        /// List of successfully registered services
        /// </summary>
        public List<string> RegisteredServices { get; set; } = new();

        /// <summary>
        /// Summary of validation results
        /// </summary>
        public string Summary
        {
            get
            {
                if (IsValid)
                {
                    return $"All services are valid. {RegisteredServices.Count} services registered successfully.";
                }
                else
                {
                    return $"Validation failed. {Issues.Count} issue(s) found: {string.Join(", ", Issues)}";
                }
            }
        }

        /// <summary>
        /// Detailed report of validation results
        /// </summary>
        public string DetailedReport
        {
            get
            {
                var report = new List<string>
                {
                    "Service Container Validation Report",
                    "=" + new string('=', 40),
                    ""
                };

                if (RegisteredServices.Count > 0)
                {
                    report.Add("Successfully Registered Services:");
                    foreach (var service in RegisteredServices)
                    {
                        report.Add($"  ✓ {service}");
                    }
                    report.Add("");
                }

                if (Issues.Count > 0)
                {
                    report.Add("Issues Found:");
                    foreach (var issue in Issues)
                    {
                        report.Add($"  ✗ {issue}");
                    }
                    report.Add("");
                }

                report.Add($"Overall Status: {(IsValid ? "VALID" : "INVALID")}");
                
                return string.Join(Environment.NewLine, report);
            }
        }
    }
}
